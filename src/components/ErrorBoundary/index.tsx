import React, { useEffect, useMemo } from 'react';
import { useRouteError, isRouteErrorResponse } from 'react-router-dom';
import { raptorReport } from '@common';
import { Empty } from '@roo/roo-b-mobile';

interface IProps {
  fallback?: React.ReactElement;
  children: React.ReactNode;
}

interface IState {
  error: Error | null;
}

const defaultFallbackInfo = {
  title: '页面显示有误',
  desc: '未知报错',
  img: 'https://s3plus.meituan.net/v1/mss_e2821d7f0cfe4ac1bf9202ecf9590e67/cdn-prod/file:db7f371d/noContent.png',
  style: {
    padding: '20px',
  },
};

/**
 * 通用ErrorBoundary组件
 */
class ErrorBoundary extends React.Component<IProps, IState> {
  constructor(props: any) {
    super(props);
    this.state = { error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { error };
  }

  componentDidCatch(error: Error) {
    raptorReport.reportCustomError({
      name: `页面无法正常渲染，请关注, 错误信息如下： ${error.name}`,
      message: error.message,
      category: 'jsError',
      level: 'ERROR',
    });
  }

  render() {
    const error = this.state.error;
    const { fallback, children } = this.props;
    const { title, desc, img } = defaultFallbackInfo;

    if (error) {
      return (
        fallback || (
          <Empty image={img} description={error instanceof Error ? error.message || desc : desc} title={title} />
        )
      );
    }
    return children;
  }
}

/**
 * 路由错误边界组件
 */
export function RouteErrorBoundary({ fallback }: Pick<IProps, 'fallback'>): React.ReactElement {
  const error = useRouteError();
  const { title, desc, img, style } = defaultFallbackInfo;

  const { errorDetail, errorName } = useMemo(() => {
    if (isRouteErrorResponse(error)) {
      return {
        errorDetail: error.data?.message || '',
        errorName: `路由错误[${error.status}]${error.statusText || ''}`,
      };
    }
    if (error instanceof Error) {
      return {
        errorDetail: error.message || '',
        errorName: `组件错误[${error.name}]`,
      };
    }
    return {
      errorDetail: '路由错误',
      errorName: '未知错误',
    };
  }, [error]);

  useEffect(() => {
    if (error) {
      raptorReport.reportCustomError({
        name: `页面无法正常渲染，请关注, 错误信息如下： ${errorName}`,
        message: errorDetail,
        category: 'jsError',
        level: 'ERROR',
      });
    }
  }, [error, errorDetail, errorName]);

  return fallback || <Empty image={img} description={errorDetail || desc} title={title} style={style} />;
}

export default ErrorBoundary;
