.container {
  position: relative;
  display: inline-block;
}

.image {
  display: block;
  width: 100%;
  height: auto;
}

.overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 25%; /* 图片高度的四分之一 */
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 2px;
  box-sizing: border-box;
  min-width: 0;
}

.overlayText {
  color: #ffffff;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  //text-overflow: ellipsis;
} 