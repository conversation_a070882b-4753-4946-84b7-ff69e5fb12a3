# ImageWithOverlay 组件

一个带有底部蒙层的图片组件，蒙层高度为图片高度的四分之一。

## 功能特性

- 底部蒙层，颜色为 #989895
- 蒙层上显示白色字体
- 蒙层高度为图片高度的四分之一
- 支持自定义样式类名
- 文字自动换行和省略处理

## 使用示例

```tsx
import ImageWithOverlay from "@/components/ImageWithOverlay";

// 基础使用
<ImageWithOverlay
  src="https://example.com/image.png"
  alt="图片描述"
  overlayText="这是蒙层文字"
/>

// 自定义样式
<ImageWithOverlay
  src="https://example.com/image.png"
  alt="图片描述"
  overlayText="自定义样式"
  className="custom-container"
  imageClassName="custom-image"
/>
```

## Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| src | string | 是 | - | 图片地址 |
| alt | string | 是 | - | 图片alt属性 |
| overlayText | string | 是 | - | 蒙层显示的文字 |
| className | string | 否 | - | 容器自定义样式类名 |
| imageClassName | string | 否 | - | 图片自定义样式类名 |

## 样式说明

- 蒙层颜色：`#989895`
- 文字颜色：`#ffffff`
- 蒙层高度：图片高度的25%
- 文字大小：12px
- 文字支持最多2行显示，超出部分省略 