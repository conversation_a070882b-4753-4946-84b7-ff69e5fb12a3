import React from "react";
import styles from "./index.module.scss";

interface ImageWithOverlayProps {
  src: string;
  alt: string;
  overlayText: string;
  className?: string;
  imageClassName?: string;
}

const ImageWithOverlay: React.FC<ImageWithOverlayProps> = ({
  src,
  alt,
  overlayText,
  className,
  imageClassName,
}) => {
  return (
    <div className={`${styles.container} ${className || ""}`}>
      <img
        src={src}
        alt={alt}
        className={`${styles.image} ${imageClassName || ""}`}
      />
      {/* <div className={styles.overlay}>
        <span className={styles.overlayText}>{overlayText}</span>
      </div> */}
    </div>
  );
};

export default ImageWithOverlay; 