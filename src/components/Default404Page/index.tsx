import React from 'react';
import { RouteObject, Link } from 'react-router-dom';

interface IProps {
  routes: RouteObject[];
}

function Default404Page({ routes }: IProps) {
  const getAllRoutePaths = (routes: RouteObject[]): React.ReactNode[] => {
    const paths: React.ReactNode[] = [];

    routes.forEach((route) => {
      if (route.path && route.path !== '*') {
        // 处理路径拼接，确保斜杠正确
        paths.push(
          <li key={route.path}>
            <Link to={route.path ?? '/'}>{route.path}</Link>
          </li>,
        );

        // 递归处理子路由
        if (route.children) {
          paths.push(...getAllRoutePaths(route.children || []));
        }
      }
    });

    return paths;
  };

  return (
    <div>
      <h1>当前为默认的 404 路由页面</h1>
      <p>此路由仅在线下环境注入以方便调试，线上不会自动注入</p>
      <p>如业务需要自定义的默认路由页面，可随时在 src/router/index.tsx 文件中删除注入逻辑</p>
      <br />
      <br />
      <p>当前业务存在以下路由，可点击跳转：</p>
      <ul>{getAllRoutePaths(routes)}</ul>
    </div>
  );
}
export default Default404Page;
