import { NavigationBar } from "@roo/roo-b-mobile";
import { env } from "@wmfe/intelligent_common";
import styles from "./index.module.scss";
import React from "react";
import useSafeArea from "../../hooks/use-safe-area";

interface ErrorPageProps {
  error: string;
  title: string;
  handleRetry?: () => void;
  onBack: () => void;
}

function ErrorPage(props: ErrorPageProps) {
  const { error, title, handleRetry, onBack } = props;
  console.log("env.isWX:", env.isWX);

  return (
    <div className={styles.errorPageWrapper}>
      <NavigationBar
        height={50}
        fixed={false}
        onBack={onBack}
        title={title}
        safeAreaInsetTop
      />

      <div className={styles.errorContainer}>
        <div className={styles.errorText}>
          <img
            src="https://p0.meituan.net/tuling/ccf269f4f060672592e9269481b9516f2090.png"
            alt="提示图"
            className={styles.errorImage}
          />
          {error}
        </div>
        {handleRetry && (
          <div className={styles.retryButton} onClick={handleRetry}>
            点击重试
          </div>
        )}
      </div>
    </div>
  );
}

export default ErrorPage;
