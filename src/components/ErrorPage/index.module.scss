.error-page-wrapper {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.error {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    background-color: #ffffff;
    flex: 1;
    overflow: hidden;
  }

  &-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 24px;
  }
  &-image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    object-fit: contain;
  }
}

.retry-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 30px;
  color: #ff2d19;
  font-size: 14px;
  border: 1px solid #ff2d19;
  border-radius: 5000px;
  margin-top: 20px;
  font-weight: 500px;
  font-family: PingFang SC;
}
