import { env } from "@wmfe/intelligent_common";

/**
 * 安全地添加或更新URL参数
 * @param url 原始URL
 * @param paramName 参数名
 * @param paramValue 参数值
 * @returns 处理后的URL
 */
export const addUrlParam = (url: string, paramName: string, paramValue: string): string => {
  if (!url || !paramName || paramValue === undefined || paramValue === null || paramValue === '') {
    return url;
  }

  try {
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    
    // 使用 set 方法：如果参数不存在则添加，如果存在则更新
    searchParams.set(paramName, paramValue);
    
    return urlObj.toString();
  } catch (error) {
    // 如果URL解析失败，尝试手动处理
    console.warn('URL解析失败，尝试手动处理:', error);
    return addUrlParamManually(url, paramName, paramValue);
  }
};

/**
 * 手动添加URL参数（当URL构造函数失败时的备用方案）
 * @param url 原始URL
 * @param paramName 参数名
 * @param paramValue 参数值
 * @returns 处理后的URL
 */
const addUrlParamManually = (url: string, paramName: string, paramValue: string): string => {
  if (!url || !paramName || paramValue === undefined || paramValue === null || paramValue === '') {
    return url;
  }

  // 移除参数值中的特殊字符，防止URL注入
  const safeParamValue = encodeURIComponent(paramValue);
  
  // 检查URL是否已经有参数
  const hasParams = url.includes('?');
  const separator = hasParams ? '&' : '?';
  
  // 检查参数是否已存在
  const paramRegex = new RegExp(`[?&]${paramName}=([^&]*)`);
  if (paramRegex.test(url)) {
    // 参数已存在，替换它
    return url.replace(paramRegex, `${hasParams ? '&' : '?'}${paramName}=${safeParamValue}`);
  } else {
    // 参数不存在，添加它
    return `${url}${separator}${paramName}=${safeParamValue}`;
  }
};

/**
 * 批量添加URL参数
 * @param url 原始URL
 * @param params 参数对象
 * @returns 处理后的URL
 */
export const addUrlParams = (url: string, params: Record<string, string>): string => {
  if (!url || !params || typeof params !== 'object') {
    return url;
  }

  let resultUrl = url;
  
  Object.entries(params).forEach(([key, value]) => {
    if (key && value !== undefined && value !== null && value !== '') {
      resultUrl = addUrlParam(resultUrl, key, value);
    }
  });
  
  return resultUrl;
};

/**
 * 解析URL参数并修改指定参数
 * @param url 需要处理的URL
 * @param paramName 要修改的参数名，如果为空则不修改
 * @param paramValue 要设置的参数值，如果为空则不修改
 * @returns 修改后的URL
 */
const modifyUrlParams = (url: string, paramName?: string, paramValue?: string): string => {
  // 如果参数名或参数值为空，直接返回原始URL
  if (!paramName || paramValue === undefined || paramValue === null || paramValue === '') {
    return url;
  }

  return addUrlParam(url, paramName, paramValue);
};

/**
 * 根据环境将URL拼接成相应的scheme格式
 * @param url 需要拼接的URL
 * @param notitlebar 是否显示标题栏，默认为1
 * @param paramName 要修改的参数名，如果为空则不修改
 * @param paramValue 要设置的参数值，如果为空则不修改
 * @returns 拼接后的scheme URL
 */
export const buildSchemeUrl = (
  url: string, 
  notitlebar: number = 1, 
  paramName?: string, 
  paramValue?: string
): string => {
  // 修改URL参数，将指定参数设置为指定值（如果提供了参数）
  const modifiedUrl = modifyUrlParams(url, paramName, paramValue);
  
  // 检查是否为外卖环境
  if (env.isWM) {
    return `@meituanwaimai://waimai.meituan.com/web?url=${encodeURIComponent(modifiedUrl)}&notitlebar=${notitlebar}`;
  }
  return `@imeituan://www.meituan.com/web?url=${encodeURIComponent(modifiedUrl)}&notitlebar=${notitlebar}`;
};
