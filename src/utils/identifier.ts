/**
 * @description 生成唯一标识符
 * @returns {string} 返回一个唯一的标识符字符串
 */
export const generateIdentifier = (): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${timestamp}_${random}`;
};

/**
 * @description 生成短格式的唯一标识符
 * @returns {string} 返回短格式的唯一标识符字符串
 */
export const generateShortId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}; 