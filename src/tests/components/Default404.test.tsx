// 引入被测组件及相关库
import React from 'react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { render, screen } from '@testing-library/react';
import Default404Page from '../../components/Default404Page';
import '@testing-library/jest-dom';

// 在文件顶部添加类型定义
interface RouteType {
  path: string;
  element: React.ReactNode;
}

/**
 * Default404Page组件测试
 */
describe('Default404Page', () => {
  // 测试用例1：渲染默认404页面，不包含任何路由
  test('渲染默认404页面，不包含任何路由', () => {
    // arrange
    const routes: RouteType[] = [];

    // act
    render(
      <MemoryRouter>
        <Default404Page routes={routes} />
      </MemoryRouter>,
    );

    // assert
    expect(screen.getByText('当前为默认的 404 路由页面')).toBeInTheDocument();
  });

  // 测试用例2：渲染默认404页面，包含特定路由
  test('渲染默认404页面，包含特定路由', () => {
    // arrange
    const routes: RouteType[] = [
      { path: '/home', element: <div>Home</div> },
      { path: '*', element: <div>404</div> },
    ];

    // act
    render(
      <MemoryRouter>
        <Default404Page routes={routes} />
      </MemoryRouter>,
    );

    // assert
    expect(screen.getByText('当前为默认的 404 路由页面')).toBeInTheDocument();
    expect(screen.getByText('当前业务存在以下路由，可点击跳转：')).toBeInTheDocument();
    expect(screen.getByText('/home')).toBeInTheDocument();
    expect(screen.queryByText('*')).not.toBeInTheDocument(); // 确认*路由不被渲染
  });

  // 测试用例3：点击路由链接
  test('点击路由链接', () => {
    // arrange
    const routes: RouteType[] = [{ path: '/home', element: <div>Home</div> }];

    // act
    render(
      <MemoryRouter initialEntries={['/']}>
        <Routes>
          <Route path="/" element={<Default404Page routes={routes} />} />
          <Route path="/home" element={<div>Home Page</div>} />
        </Routes>
      </MemoryRouter>,
    );
    const linkElement = screen.getByText('/home');

    // assert
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute('href', '/home');
  });
});
