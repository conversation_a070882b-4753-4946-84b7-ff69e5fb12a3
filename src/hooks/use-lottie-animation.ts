import { useRef, useState, useCallback, useEffect } from 'react';

// 全局lottie数据缓存
const lottieDataCache = new Map<string, any>();

// 全局预加载状态管理
const preloadPromises = new Map<string, Promise<any>>();

interface UseLottieAnimationOptions {
  loop?: boolean;
  autoplay?: boolean;
  renderer?: 'svg' | 'canvas' | 'html';
}

interface UseLottieAnimationReturn {
  animationRef: React.RefObject<HTMLDivElement>;
  isPlaying: boolean;
  playAnimation: (animationData: any, onComplete?: () => void) => void;
  stopAnimation: () => void;
  pauseAnimation: () => void;
  resumeAnimation: () => void;
  loadLottieData: (url: string) => Promise<any>; // 新增：加载lottie数据的方法
}

/**
 * 预加载lottie数据的hook
 * @param urls 需要预加载的lottie文件URL数组
 * @returns 预加载状态和数据
 */
export const usePreloadLottieData = (urls: string[]) => {
  const [loadedData, setLoadedData] = useState<Map<string, any>>(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAllData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const promises = urls.map(async (url) => {
          // 如果已经有预加载的promise，直接使用
          if (preloadPromises.has(url)) {
            return { url, data: await preloadPromises.get(url)! };
          }

          // 检查缓存
          if (lottieDataCache.has(url)) {
            return { url, data: lottieDataCache.get(url) };
          }

          // 创建新的加载promise
          const loadPromise = fetch(url)
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              lottieDataCache.set(url, data);
              return data;
            });

          preloadPromises.set(url, loadPromise);
          const data = await loadPromise;
          return { url, data };
        });

        const results = await Promise.all(promises);
        const newLoadedData = new Map();
        results.forEach(({ url, data }) => {
          newLoadedData.set(url, data);
        });

        setLoadedData(newLoadedData);
        setIsLoading(false);
        console.log('[DEBUG] 预加载完成，共加载', results.length, '个lottie文件');
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败');
        setIsLoading(false);
        console.error('[ERROR] 预加载lottie数据失败:', err);
      }
    };

    if (urls.length > 0) {
      loadAllData();
    } else {
      setIsLoading(false);
    }
  }, [urls.join(',')]); // 使用urls的字符串形式作为依赖

  return {
    loadedData,
    isLoading,
    error,
  };
};

/**
 * 通用Lottie动画管理hook
 * @param options 动画配置选项
 * @returns 动画控制方法和状态
 */
export const useLottieAnimation = (options: UseLottieAnimationOptions = {}): UseLottieAnimationReturn => {
  const {
    loop = false,
    autoplay = true,
    renderer = 'svg'
  } = options;

  const animationRef = useRef<HTMLDivElement>(null);
  const instanceRef = useRef<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // 新增：全局lottie数据加载方法，带缓存
  const loadLottieData = useCallback(async (url: string) => {
    // 检查缓存
    if (lottieDataCache.has(url)) {
      console.log('[DEBUG] 使用缓存的lottie数据:', url);
      return lottieDataCache.get(url);
    }

    // 检查是否有预加载的promise
    if (preloadPromises.has(url)) {
      console.log('[DEBUG] 使用预加载的lottie数据:', url);
      return await preloadPromises.get(url)!;
    }

    console.log('[DEBUG] 加载lottie数据:', url);
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    
    // 缓存数据
    lottieDataCache.set(url, data);
    console.log('[DEBUG] lottie数据已缓存:', url);
    
    return data;
  }, []);

  const playAnimation = useCallback((animationData: any, onComplete?: () => void) => {
    if (!animationRef.current) {
      console.warn('动画容器未准备好');
      return;
    }

    // 清理之前的动画实例
    if (instanceRef.current) {
      instanceRef.current.destroy();
      instanceRef.current = null;
    }

    // 按需加载 lottie-web
    import(
      /* webpackMode: "lazy" */
      /* webpackPreload: true */
      'lottie-web'
    )
      .then((lottie) => {
        // 再次检查容器是否存在，因为异步加载后可能已经被卸载
        if (!animationRef.current) return;

        // 创建新的动画实例
        instanceRef.current = lottie.default.loadAnimation({
          container: animationRef.current,
          renderer,
          loop,
          autoplay,
          animationData,
        });

        setIsPlaying(true);

        // 动画完成回调
        instanceRef.current.addEventListener("complete", () => {
          console.log('动画播放完成');
          setIsPlaying(false);
          if (instanceRef.current) {
            instanceRef.current.destroy();
            instanceRef.current = null;
          }
          onComplete?.();
        });

        // 错误处理
        instanceRef.current.addEventListener("error", (error: any) => {
          console.error('Lottie animation error:', error);
          setIsPlaying(false);
          if (instanceRef.current) {
            instanceRef.current.destroy();
            instanceRef.current = null;
          }
        });

        // 加载完成回调
        instanceRef.current.addEventListener("DOMLoaded", () => {
          console.log('动画DOM加载完成');
        });
      })
      .catch((error) => {
        console.error('Failed to load lottie-web:', error);
      });
  }, [loop, autoplay, renderer]);

  const stopAnimation = useCallback(() => {
    if (instanceRef.current) {
      instanceRef.current.destroy();
      instanceRef.current = null;
    }
    setIsPlaying(false);
  }, []);

  const pauseAnimation = useCallback(() => {
    if (instanceRef.current && isPlaying) {
      instanceRef.current.pause();
      setIsPlaying(false);
    }
  }, [isPlaying]);

  const resumeAnimation = useCallback(() => {
    if (instanceRef.current && !isPlaying) {
      instanceRef.current.play();
      setIsPlaying(true);
    }
  }, [isPlaying]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (instanceRef.current) {
        instanceRef.current.destroy();
        instanceRef.current = null;
      }
    };
  }, []);

  return {
    animationRef,
    isPlaying,
    playAnimation,
    stopAnimation,
    pauseAnimation,
    resumeAnimation,
    loadLottieData, // 新增：返回加载方法
  };
}; 