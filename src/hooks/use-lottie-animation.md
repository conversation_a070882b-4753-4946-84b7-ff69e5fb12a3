# useLottieAnimation Hook 使用文档

## 概述

`useLottieAnimation` 是一个通用的 Lottie 动画管理 hook，提供了完整的动画生命周期管理，包括播放、暂停、恢复、停止和清理功能。同时提供了全局缓存和预加载机制，避免重复请求相同的 lottie.json 文件。

## 特性

- ✅ 自动资源清理
- ✅ 错误处理
- ✅ 动画状态管理
- ✅ 完成回调支持
- ✅ 可配置的动画选项
- ✅ TypeScript 支持
- ✅ **全局数据缓存** - 避免重复请求相同的 lottie 文件
- ✅ **预加载机制** - 支持批量预加载多个 lottie 文件
- ✅ **Promise 去重** - 相同 URL 的请求会被合并

## 基本用法

```tsx
import { useLottieAnimation } from '@/hooks/use-lottie-animation';
import lottieData from '@/assets/lottie/your-animation.json';

function MyComponent() {
  const animation = useLottieAnimation();
  
  const handlePlayAnimation = () => {
    animation.playAnimation(lottieData, () => {
      console.log('动画播放完成');
    });
  };

  return (
    <div>
      <button onClick={handlePlayAnimation}>播放动画</button>
      <div ref={animation.animationRef} className="animation-container" />
    </div>
  );
}
```

## 预加载用法

### 1. 使用 usePreloadLottieData Hook

```tsx
import { usePreloadLottieData } from '@/hooks/use-lottie-animation';

function MyComponent() {
  const urls = [
    '/assets/lottie/animation1.json',
    '/assets/lottie/animation2.json'
  ];
  
  const { loadedData, isLoading, error } = usePreloadLottieData(urls);

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败: {error}</div>;

  return (
    <div>
      {/* 使用预加载的数据 */}
      {loadedData.get('/assets/lottie/animation1.json') && (
        <AnimationComponent data={loadedData.get('/assets/lottie/animation1.json')} />
      )}
    </div>
  );
}
```

### 2. 在父组件中预加载，传递给子组件

```tsx
// 父组件
function ParentComponent() {
  const urls = ['/assets/lottie/animation.json'];
  const { loadedData, isLoading } = usePreloadLottieData(urls);
  
  return (
    <div>
      {[1, 2, 3].map(index => (
        <ChildComponent 
          key={index}
          preloadedData={loadedData.get('/assets/lottie/animation.json')}
          isDataLoaded={!isLoading}
        />
      ))}
    </div>
  );
}

// 子组件
function ChildComponent({ preloadedData, isDataLoaded }) {
  const animation = useLottieAnimation();
  
  useEffect(() => {
    if (isDataLoaded && preloadedData) {
      animation.playAnimation(preloadedData);
    }
  }, [isDataLoaded, preloadedData]);
  
  return <div ref={animation.animationRef} />;
}
```

## API 参考

### useLottieAnimation 参数

```tsx
interface UseLottieAnimationOptions {
  loop?: boolean;        // 是否循环播放，默认 false
  autoplay?: boolean;    // 是否自动播放，默认 true
  renderer?: 'svg' | 'canvas' | 'html';  // 渲染器类型，默认 'svg'
}
```

### useLottieAnimation 返回值

```tsx
interface UseLottieAnimationReturn {
  animationRef: React.RefObject<HTMLDivElement>;  // 动画容器引用
  isPlaying: boolean;                             // 是否正在播放
  playAnimation: (animationData: any, onComplete?: () => void) => void;  // 播放动画
  stopAnimation: () => void;                      // 停止动画
  pauseAnimation: () => void;                     // 暂停动画
  resumeAnimation: () => void;                    // 恢复动画
  loadLottieData: (url: string) => Promise<any>; // 加载lottie数据（带缓存）
}
```

### usePreloadLottieData 参数

```tsx
// urls: string[] - 需要预加载的lottie文件URL数组
```

### usePreloadLottieData 返回值

```tsx
interface UsePreloadLottieDataReturn {
  loadedData: Map<string, any>;  // 已加载的数据，key为URL，value为lottie数据
  isLoading: boolean;             // 是否正在加载
  error: string | null;           // 错误信息
}
```

## 高级用法

### 1. 配置动画选项

```tsx
const animation = useLottieAnimation({
  loop: true,
  autoplay: false,
  renderer: 'canvas'
});
```

### 2. 动画状态控制

```tsx
function AnimationController() {
  const animation = useLottieAnimation();
  
  const handlePlay = () => {
    animation.playAnimation(lottieData);
  };
  
  const handlePause = () => {
    animation.pauseAnimation();
  };
  
  const handleResume = () => {
    animation.resumeAnimation();
  };
  
  const handleStop = () => {
    animation.stopAnimation();
  };

  return (
    <div>
      <button onClick={handlePlay}>播放</button>
      <button onClick={handlePause}>暂停</button>
      <button onClick={handleResume}>恢复</button>
      <button onClick={handleStop}>停止</button>
      <div ref={animation.animationRef} />
    </div>
  );
}
```

### 3. 使用缓存机制

```tsx
function OptimizedComponent() {
  const animation = useLottieAnimation();
  
  const handleLoadAndPlay = async () => {
    // 使用带缓存的加载方法
    const data = await animation.loadLottieData('/assets/lottie/animation.json');
    animation.playAnimation(data);
  };

  return (
    <div>
      <button onClick={handleLoadAndPlay}>加载并播放</button>
      <div ref={animation.animationRef} />
    </div>
  );
}
```

## 性能优化

### 1. 避免重复请求

通过全局缓存机制，相同的 lottie.json 文件只会被请求一次：

```tsx
// 第一次请求
const data1 = await animation.loadLottieData('/assets/lottie/animation.json');

// 第二次请求 - 直接使用缓存
const data2 = await animation.loadLottieData('/assets/lottie/animation.json');
// data1 === data2，不会发起新的网络请求
```

### 2. Promise 去重

如果多个组件同时请求相同的文件，只会发起一个网络请求：

```tsx
// 组件A和组件B同时请求相同的文件
// 只会发起一个网络请求，两个组件都会收到相同的数据
```

### 3. 预加载策略

在父组件中预加载数据，避免子组件重复请求：

```tsx
// 父组件预加载
const { loadedData } = usePreloadLottieData(['/assets/lottie/animation.json']);

// 子组件直接使用预加载的数据
<ChildComponent data={loadedData.get('/assets/lottie/animation.json')} />
```

## 错误处理

```tsx
function ErrorHandlingComponent() {
  const { loadedData, isLoading, error } = usePreloadLottieData(['/assets/lottie/animation.json']);
  
  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败: {error}</div>;
  
  return <AnimationComponent data={loadedData.get('/assets/lottie/animation.json')} />;
}
```

## 最佳实践

1. **预加载常用动画**：在应用启动时预加载常用的 lottie 文件
2. **使用缓存**：优先使用 `loadLottieData` 方法而不是直接 fetch
3. **错误处理**：始终处理加载失败的情况
4. **资源清理**：组件卸载时会自动清理动画实例
5. **性能监控**：通过控制台日志监控缓存命中率

## 注意事项

- 缓存是全局的，在页面刷新前会一直存在
- 预加载的 Promise 会在所有引用被清理后自动清理
- 动画实例会在组件卸载时自动销毁
- 建议在生产环境中移除 DEBUG 日志 