import { useState, useEffect } from 'react';

interface SafeAreaInsets {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

interface UseSafeAreaReturn {
  safeAreaInsets: SafeAreaInsets;
  isLoading: boolean;
  error: string | null;
}

const useSafeArea = (): UseSafeAreaReturn => {
  // 设置默认的安全区域值
  const defaultSafeAreaInsets: SafeAreaInsets = {
    top: 44, // iPhone状态栏高度
    bottom: 34, // iPhone底部安全区域
    left: 0,
    right: 0
  };

  const [safeAreaInsets, setSafeAreaInsets] = useState<SafeAreaInsets>(defaultSafeAreaInsets);
  const [isLoading, setIsLoading] = useState(false); // 改为false，因为已经有默认值
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getSafeAreaInsets = () => {
      console.log('开始获取安全区域信息');
      
      // 检查是否在KNB环境中
      if (typeof window !== 'undefined' && window.KNB) {
        console.log('检测到KNB环境，使用KNB.getSafeArea');
        try {
          // 使用KNB.ready确保KNB已准备就绪
          window.KNB.ready(() => {
            window.KNB?.use('getSafeArea', {
              success: function(result: SafeAreaInsets) {
                console.log('KNB getSafeArea success:', result);
                const { top, bottom, left, right } = result;
                setSafeAreaInsets({ top, bottom, left, right });
                setIsLoading(false);
              },
              fail: function(err: any) {
                console.warn('KNB getSafeArea failed:', err);
                // 降级到CSS env()函数
                fallbackToCSSEnv();
              }
            });
          });
        } catch (e) {
          console.warn('KNB getSafeArea error:', e);
          fallbackToCSSEnv();
        }
      } else {
        console.log('未检测到KNB环境，使用CSS env()函数');
        // 不在KNB环境中，使用CSS env()函数作为降级方案
        fallbackToCSSEnv();
      }
    };

    const fallbackToCSSEnv = () => {
      console.log('使用CSS env()函数作为降级方案');
      // 使用CSS env()函数作为降级方案
      const getCSSEnvValue = (property: string): number => {
        try {
          const value = getComputedStyle(document.documentElement).getPropertyValue(property);
          const parsedValue = parseInt(value);
          console.log(`${property}: ${value} -> ${parsedValue}`);
          return isNaN(parsedValue) ? 0 : parsedValue;
        } catch (e) {
          console.warn(`获取CSS env值失败: ${property}`, e);
          return 0;
        }
      };

      // 尝试多种CSS env()函数写法
      const top = getCSSEnvValue('env(safe-area-inset-top)') || 
                  getCSSEnvValue('constant(safe-area-inset-top)') || 
                  getCSSEnvValue('--safe-area-inset-top') || defaultSafeAreaInsets.top;
      const bottom = getCSSEnvValue('env(safe-area-inset-bottom)') || 
                     getCSSEnvValue('constant(safe-area-inset-bottom)') || 
                     getCSSEnvValue('--safe-area-inset-bottom') || defaultSafeAreaInsets.bottom;
      const left = getCSSEnvValue('env(safe-area-inset-left)') || 
                   getCSSEnvValue('constant(safe-area-inset-left)') || 
                   getCSSEnvValue('--safe-area-inset-left') || defaultSafeAreaInsets.left;
      const right = getCSSEnvValue('env(safe-area-inset-right)') || 
                    getCSSEnvValue('constant(safe-area-inset-right)') || 
                    getCSSEnvValue('--safe-area-inset-right') || defaultSafeAreaInsets.right;

      const result = { top, bottom, left, right };
      console.log('CSS env()结果:', result);
      setSafeAreaInsets(result);
      setIsLoading(false);
    };

    // 添加超时处理，避免一直加载
    const timeoutId = setTimeout(() => {
      console.warn('获取安全区域超时，使用默认值');
      setSafeAreaInsets(defaultSafeAreaInsets);
      setIsLoading(false);
    }, 2000); // 缩短超时时间

    getSafeAreaInsets();

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return { safeAreaInsets, isLoading, error };
};

export default useSafeArea; 