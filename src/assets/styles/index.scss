@import '@common/styles/index.scss';

// 字体预加载优化
@font-face {
  font-family: 'M<PERSON>';
  src: url('../fonts/MF YuanHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap; // 使用swap策略，减少布局偏移
  font-preload: true; // 预加载字体
}

@font-face {
  font-family: 'FZLanTYJ';
  src: url('../fonts/FZLanTYJW_Zhong.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  font-preload: true;
}

@font-face {
  font-family: 'Meituan Type-Light';
  src: url('../fonts/Meituan Type-Regular.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  font-preload: true;
}

// 如果需要使用FZLanTingYuanS-DB-GB字体，请将对应的字体文件放在fonts目录下
// @font-face {
//   font-family: 'FZLanTingYuanS-DB-GB';
//   src: url('../fonts/FZLanTingYuanS-DB-GB.ttf') format('truetype');
//   font-weight: normal;
//   font-style: normal;
//   font-display: swap;
//   font-preload: true;
// }

