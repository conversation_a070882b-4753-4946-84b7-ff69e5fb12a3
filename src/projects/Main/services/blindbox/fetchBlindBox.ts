import { Request as request } from "@wmfe/intelligent_common";
import { Toast } from "@roo/roo-b-mobile";

/**
 * @description 卡牌信息接口
 */
export interface ICard {
  /**
   * @description 卡牌ID
   */
  cardId: number;
  /**
   * @description 卡牌名称
   */
  cardName: string;
  /**
   * @description 卡牌图片URL
   */
  imageUrl: string;
  /**
   * @description 卡牌类型
   */
  cardType: number;
}

/**
 * @description 收藏卡牌信息接口
 */
export interface ICollectionCard extends ICard {
  /**
   * @description 高达页跳转链接
   */
  gaodaJumpUrl: string;
  /**
   * @description 未获得时的图片
   */
  unOwnedImageUrl: string;
  /**
   * @description 旋转动图
   */
  rotateWebpUrl: string;
  /**
   * @description 分享图片
   */
  shareImageUrl: string;
  /**
   * @description 分享图片原图
   */
  shareOriginImageUrl: string;
  /**
   * @description 收集状态
   */
  collectionStatus: number;
  /**
   * @description 礼品令牌
   */
  giftToken: string | null;
  /**
   * @description 数量
   */
  quantity: number;
  /**
   * @description 显示顺序
   */
  displayOrder: number;
}

/**
 * @description 赠送卡牌信息接口
 */
export interface IGiveCardInfo {
  /**
   * @description 赠送者用户ID
   */
  giverUserId: number;
  /**
   * @description 赠送者用户名
   */
  giverUserName: string;
  /**
   * @description 是否已接受
   */
  accepted: boolean;
  /**
   * @description 卡牌信息
   */
  cardInfo: ICard;
  /**
   * @description 旋转动图
   */
  rotateWebpUrl: string;
}

/**
 * @description 已抽取的卡牌信息接口
 */
export interface IDrawnCard extends ICard {
  /**
   * @description 推荐文案
   */
  recommendText: string;
  /**
   * @description 动画装饰图
   */
  decorationImages: Array<string>;
  /**
   * @description 高达页跳转链接
   */
  gaodaJumpUrl: string;
  /**
   * @description 盲盒展示的图片
   */
  inBoxImageUrl: string;

  imageUrl: string;
  /**
   * @description 抽取槽位
   */
  drawSlot: number;
  /**
   * @description 抽取顺序
   */
  drawOrder: number;
  /**
   * @description 抽取状态
   */
  drawStatus: number;
  /**
   * @description 是否拥有
   */
  owned: boolean;
}

/**
 * @description 卡牌收集信息接口
 */
export interface ICardCollectionInfo {
  /**
   * @description 所有卡牌
   */
  allCards: Array<ICollectionCard>;
}

/**
 * @description 今日抽取状态接口
 */
export interface IDrawStatusToday {
  /**
   * @description 今日总机会
   */
  totalChancesToday: number;
  /**
   * @description 今日剩余机会
   */
  remainingChancesToday: number;
  /**
   * @description 今日已抽取的卡牌
   */
  drawnTodayCards: Array<IDrawnCard>;
}

export interface IHomeInitData {
  /**
   * @description 卡牌收集信息
   */
  cardCollectionInfo: ICardCollectionInfo;
  /**
   * @description 今日抽取状态
   */
  drawStatusToday: IDrawStatusToday;
}

/**
 * 统一的错误处理函数
 * @param err 错误对象
 * @param defaultMessage 默认错误信息
 * @returns 处理后的错误信息
 */
const handleError = (err: any, defaultMessage: string = "服务异常，请稍后再试"): string => {
  console.error("API错误详情:", err);
  
  // 按优先级提取错误信息
  const errorMessage = 
    err?.data?.msg || 
    err?.data?.message || 
    err?.message || 
    err?.msg || 
    defaultMessage;
  
  // 显示错误Toast
  Toast.open({
    content: errorMessage,
  });
  
  return errorMessage;
};

/**
 * 获取盲盒卡牌首页初始化数据
 * @returns 卡牌首页数据
 */
export async function fetchBlindboxHomeInit(): Promise<IHomeInitData> {
  try {
    return await request<{}, IHomeInitData>({
      url: "/api/blindbox/card/homeInit",
      method: "GET",
      data: {},
    });
  } catch (err: any) {
    if (err?.data?.code === 5002 || err?.data?.code === 50009) {
      // 当 code 为 5002 时，返回默认数据
      return {
        cardCollectionInfo: {
          allCards: [
            {
              cardId: 1,
              cardName: "茉香奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/bda44bea23bce8ad1332ed405055943930283.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 1,
            },
            {
              cardId: 2,
              cardName: "珍珠奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/45840e9043591000ae618ca00b8080eb28411.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 2,
            },
            {
              cardId: 3,
              cardName: "绿茶奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/1bf3bc86891a17209c019a6e274a10b726661.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 3,
            },
            {
              cardId: 4,
              cardName: "葡萄果茶",
              imageUrl: "",
              cardType: 2,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/0666c181f1052e9380265c2d9dc75b9b31245.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 4,
            },
            {
              cardId: 5,
              cardName: "柠檬茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/3664e91ebc764ef205fc14b43e4b7ad626597.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 5,
            },
            {
              cardId: 6,
              cardName: "柠檬水",
              imageUrl: "",
              cardType: 2,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/4ba8524d1de75db2ffd46817931515ed26851.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 6,
            },
            {
              cardId: 7,
              cardName: "杨枝甘露",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/9f04eb213e358b4bc1f1e46612711b5d30526.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 7,
            },
            {
              cardId: 8,
              cardName: "乌龙奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/2c60466005180cf011a1ffc9b0ae97d232502.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 8,
            },
            {
              cardId: 9,
              cardName: "百香果茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/7d147856c01a96fdad03f53aa610259527443.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 9,
            },
            {
              cardId: 10,
              cardName: "桃子果茶",
              imageUrl: "",
              cardType: 2,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/eb81205a5ed762e0d921b67e3f5026c132289.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 10,
            },
            {
              cardId: 11,
              cardName: "红茶奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/5fa404ab6f5a4bb380726ff9d82a82b928990.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 11,
            },
            {
              cardId: 12,
              cardName: "草莓果茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/c513ef2328bcd958618f49822d54d47029211.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 12,
            },
            {
              cardId: 13,
              cardName: "橙子果茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/9878b1cf6ee544a2f12dd3f2bdd2d86430187.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 13,
            },
            {
              cardId: 14,
              cardName: "芋泥奶茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p1.meituan.net/cagent/f0cd13ea7348ac3905048e0e7a83ea2129008.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 14,
            },
            {
              cardId: 15,
              cardName: "青提果茶",
              imageUrl: "",
              cardType: 1,
              gaodaJumpUrl: "",
              unOwnedImageUrl:
                "https://p0.meituan.net/cagent/1ce26815adcd35c696853a5d489bc12929853.png",
              rotateWebpUrl: "",
              shareImageUrl: "",
              shareOriginImageUrl: "",
              collectionStatus: 1,
              giftToken: null,
              quantity: 0,
              displayOrder: 15,
            },
          ],
        },
        drawStatusToday: {
          totalChancesToday: 3,
          remainingChancesToday: 3,
          drawnTodayCards: [],
        },
      };
    }
    const msg = handleError(err, "获取数据失败，请稍后再试");
    throw new Error(msg);
  }
}

/**
 * @description 抽卡结果接口
 */
export interface IDrawCardResult {
  /**
   * @description 卡牌ID
   */
  cardId: number;
  /**
   * @description 卡牌名称
   */
  cardName: string;
  /**
   * @description 卡牌图片URL
   */
  imageUrl: string;
  /**
   * @description 卡牌类型
   */
  cardType: number;
  /**
   * @description 推荐文案
   */
  recommendText: string;
  /**
   * @description 动画装饰图
   */
  decorationImages: Array<string>;
  /**
   * @description 高达页跳转链接
   */
  gaodaJumpUrl: string;
  /**
   * @description 抽取槽位，从1开始。
   */
  drawSlot: number;
  /**
   * @description 今日剩余抽卡机会
   */
  remainingChancesToday: number;
  /**
   * @description 是否已拥有
   */
  owned: boolean;
}

/**
 * 抽取盲盒卡牌
 * @returns 抽卡结果
 */
export function fetchDrawCard(slotPosition: number): Promise<IDrawCardResult> {
  return request<{}, IDrawCardResult>({
    url: "/api/blindbox/card/draw",
    method: "POST",
    data: {
      slotPosition: slotPosition,
    },
    errorHandle: (err) => {
      const msg = handleError(err, "抽卡失败，请稍后再试");
      throw new Error(msg);
    },
  });
}

/**
 * 获取新获得的卡牌
 * @returns 新获得的卡牌列表
 */
export function fetchNewlyAcquiredCards(): Promise<Array<number>> {
  return request<{}, Array<number>>({
    url: "/api/blindbox/card/newlyAcquired",
    method: "GET",
    data: {},
    errorHandle: (err) => {
      const msg = handleError(err, "获取新获得卡牌失败，请稍后再试");
      throw new Error(msg);
    },
  });
}


/**
 * @description 接收卡牌结果接口
 */
export interface IReceiveCardResult {
  /**
   * @description 卡牌是否接受成功
   */
  cardReceived: boolean;
  /**
   * @description 不可接受的原因（仅当 cardReceived 为 false 时有效）
   */
  cannotReceiveReason?: string;
}

/**
 * @description 取消赠送卡牌结果接口
 */
export interface ICancelGiveCardResult {
  /**
   * @description 取消赠送是否成功
   */
  cancelSuccess: boolean;
  /**
   * @description 不能取消的原因（仅当cancelSuccess为false时有效）
   */
  cannotCancelReason?: string;
}

/**
 * @description 赠送卡牌结果接口
 */
export interface IGiveCardResult {
  /**
   * @description 是否可以赠送
   */
  canGive: boolean;
  /**
   * @description 不能赠送的原因（仅当canGive为false时有效）
   */
  cannotGiveReason?: string;
  /**
   * @description 礼品令牌（仅当canGive为true时有效）
   */
  giftToken?: string | null;
  /**
   * @description 礼品链接（仅当canGive为true时有效）
   */
  giftUrl?: string | null;
}

/**
 * 点击赠送好友，获取分享端链
 * @returns 赠送卡牌结果
 */
export function fetchGiveCard(cardId: number): Promise<IGiveCardResult> {
  return request<{}, IGiveCardResult>({
    url: "/api/blindbox/card/give",
    method: "POST",
    data: {
      cardId: cardId,
    },
    errorHandle: (err) => {
      const msg = handleError(err, "赠送失败，请稍后再试");
      throw new Error(msg);
    },
  });
}

export function fetchGiveCardInfoByCard(giftToken: string): Promise<IGiveCardInfo> {
  return request<{}, IGiveCardInfo>({
    url: "/api/blindbox/card/giftInfo",
    method: "GET",
    data: {
      giftToken: giftToken,
    },
    errorHandle: (err) => {
      const msg = handleError(err, "获取礼品信息失败，请稍后再试");
      throw new Error(msg);
    },
  });
}

/**
 * 接收好友赠送的卡牌
 * @param cardId 卡牌ID
 * @param giftToken 礼品令牌
 * @returns 接收卡牌结果
 */
export function fetchReceiveCard(cardId: number, giftToken: string): Promise<IReceiveCardResult> {
  return request<{}, IReceiveCardResult>({
    url: "/api/blindbox/card/receiveGift",
    method: "POST",
    data: {
      cardId: cardId,
      giftToken: giftToken,
    },
    errorHandle: (err) => {
      const msg = handleError(err, "接收失败，请稍后再试");
      throw new Error(msg);
    },
  });
}

/**
 * 取消赠送卡牌
 * @param cardId 卡牌ID
 * @param giftToken 礼品令牌
 * @returns 取消赠送结果
 */
export function fetchCancelGiveCard(cardId: number, giftToken: string): Promise<ICancelGiveCardResult> {
  return request<{}, ICancelGiveCardResult>({
    url: "/api/blindbox/card/cancelGift",
    method: "POST",
    data: {
      cardId: cardId,
      giftToken: giftToken,
    },
    errorHandle: (err) => {
      const msg = handleError(err, "取消赠送失败，请稍后再试");
      throw new Error(msg);
    },
  });
}
