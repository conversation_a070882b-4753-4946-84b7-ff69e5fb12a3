import { requestWeb, ResponseReturnType } from '@common';

export interface IPoiInfoRequest {
  /**
   * @description 门店ID
   */
  poiId: string;
}

export interface IPoiInfoData {
  /**
   * @description 门店ID
   */
  poiId?: number;
  /**
   * @description 门店名称
   */
  poiName?: string;
}

/**
 * 请求门店信息
 * @returns
 */
export const fetchApiPoiInfo = (params: IPoiInfoRequest): Promise<ResponseReturnType<IPoiInfoData>> =>
  requestWeb.common({
    api: '/api/poiInfo',
    method: 'get',
    params,
    headers: {},
  });
