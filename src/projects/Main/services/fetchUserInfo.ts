import { requestWeb, ResponseReturnType } from '@common';

export interface IUserInfoData {
  /**
   * @description 用户名
   */
  username: string;
  /**
   * @description 用户ID
   */
  userId: number;
}

/**
 * 请求用户信息
 * @returns
 */
export const fetchUserInfo = (): Promise<ResponseReturnType<IUserInfoData>> =>
  requestWeb.common({
    api: '/api/user',
    method: 'get',
    params: {},
    headers: {},
  });
