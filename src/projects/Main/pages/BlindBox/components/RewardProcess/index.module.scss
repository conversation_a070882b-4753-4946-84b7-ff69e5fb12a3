.container{
  border-radius: 14px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 130px;
  width: 100%;
  padding: 11px;
  position: relative;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 74px;
}

.thirdPoint {
  position: absolute;
  left: 30%;
  transform: translateX(-50%);
  top: 0;
  z-index: 1;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  transform-origin: center bottom;
  
  &:hover {
    transform: translateX(-50%) scale(1.2);
  }
  
  &.clicked {
    transform: translateX(-50%) scale(1.3);
  }
}

.middlePoint {
  position: absolute;
  left: 60%;
  transform: translateX(-50%);
  top: 0;
  z-index: 1;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  transform-origin: center bottom;
  
  &:hover {
    transform: translateX(-50%) scale(1.2);
  }
  
  &.clicked {
    transform: translateX(-50%) scale(1.3);
  }
}

.endPoint {
  position: absolute;
  left: 90%;
  transform: translateX(-50%);
  top: 0;
  z-index: 1;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  transform-origin: center bottom;
  
  &:hover {
    transform: translateX(-50%) scale(1.2);
  }
  
  &.clicked {
    transform: translateX(-50%) scale(1.3);
  }
}

.imageContainer :global(.imageWithOverlay) {
  width: 59px;
  height: 45px;
}

.imageContainer :global(.imageWithOverlay img) {
  width: 59px;
  height: 45px;
  object-fit: contain;
}

.progressContainer {
  position: relative;
  width: 100%;
  height: 15px;
  margin: 10px 0;
}

.iconContainer {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 15px;
  transform: translateY(-50%);
  z-index: 2;
}

.iconContainer .thirdPoint,
.iconContainer .middlePoint,
.iconContainer .endPoint {
  top: 65%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  transform-origin: center bottom;
  
  &:hover {
    transform: translate(-50%, -50%) scale(1.2);
  }
  
  &.clicked {
    transform: translate(-50%, -50%) scale(1.3);
  }
}

.iconContainer img {
  width: 16px;
  height: 14px;
  object-fit: contain;
}

.textContainer {
  position: relative;
  width: 100%;
  height: 15px;
}

.textContainer .thirdPoint {
  position: absolute;
  left: 30%;
  transform: translateX(-50%);
  top: 0;
}

.textContainer .middlePoint {
  position: absolute;
  left: 60%;
  transform: translateX(-50%);
  top: 0;
}

.textContainer .endPoint {
  position: absolute;
  left: 90%;
  transform: translateX(-50%);
  top: 0;
}

.textContainer span {
  font-size: 11px;
  font-weight: normal;
  line-height: 15px;
  text-align: center;
  letter-spacing: 0px;
  color: #111111;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 新增的图片文字组合组件样式
.imageWithTextContainer {
  position: relative;
  width: 66px; 
  height: 74px; 
  display: flex;
  flex-direction: column;
  align-items: center;

  overflow: hidden;
}

.backgroundImage {
  position: relative;
  width: 100%;
  height: 100%;
}

.backgroundImg {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.contentOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 2px;
  box-sizing: border-box;
}

.topImage {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3px;
}

.topImg {
  width: 60px;
  height: 52px;
  object-fit: contain;
}

.bottomText{
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.text {
  font-family: 'FZLanTYJ', sans-serif;
  font-size: 10px;
  font-weight: normal;
  line-height: 10px;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #111111;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
