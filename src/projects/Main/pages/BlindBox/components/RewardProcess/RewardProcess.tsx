import React, { useState } from "react";
import styles from "./index.module.scss";
import { isNotEmptyString } from "@/projects/Main/utils/stringUtil";
import { Icon, Progress } from "@roo/roo-b-mobile";

interface RewardProcessProps {
  current: number;
  target: number;
  onPointClick?: (pointType: 'third' | 'middle' | 'end') => void;
}

// 新增的渲染组件接口
interface RenderImageWithTextProps {
  imageSrc: string;
  imageAlt?: string;
  text: string;
  className?: string;
}

// 渲染背景图片、传入图片和文字的方法
export const renderImageWithText = (props: RenderImageWithTextProps) => {
  const { imageSrc, imageAlt = "图片", text, className = "" } = props;
  
  return (
    <div className={`${styles.imageWithTextContainer} ${className}`}>
      <div className={styles.backgroundImage}>
        <img 
          src="https://p0.meituan.net/cagent/b29b75f314a5252d7773009807bb36af7601.png" 
          alt="背景图片"
          className={styles.backgroundImg}
        />
      </div>
      <div className={styles.contentOverlay}>
        <div className={styles.topImage}>
          <img 
            src={imageSrc} 
            alt={imageAlt}
            className={styles.topImg}
          />
        </div>
        <div className={styles.bottomText}>
          <span className={styles.text}>{text}</span>
        </div>
      </div>
    </div>
  );
};

function RewardProcess(props: RewardProcessProps) {
  const { current, target, onPointClick } = props;
  const progress = Math.min((current / target) * 100, 100);
  const colors = {
    from: "#FF4538",
    to: "#FFD45D",
  };

  // 状态管理每个Point的点击状态
  const [clickedPoints, setClickedPoints] = useState<{
    third: boolean;
    middle: boolean;
    end: boolean;
  }>({
    third: false,
    middle: false,
    end: false,
  });

  // 处理点击事件 - 点击后立即变回原始大小
  const handlePointClick = (pointType: 'third' | 'middle' | 'end') => {
    // 点击时短暂放大，然后立即恢复
    setClickedPoints(prev => ({
      ...prev,
      [pointType]: true
    }));
    
    // 调用父组件传入的回调函数
    if (onPointClick) {
      onPointClick(pointType);
    }
    
    // 100ms后恢复原始大小
    setTimeout(() => {
      setClickedPoints(prev => ({
        ...prev,
        [pointType]: false
      }));
    }, 100);
  };

  return (
    <div className={styles.container}>
      <div className={styles.imageContainer}>
        <div 
          className={`${styles.thirdPoint} ${clickedPoints.third ? styles.clicked : ''}`}
          onClick={() => handlePointClick('third')}
        >
          {renderImageWithText({
            imageSrc: "https://p0.meituan.net/cagent/bbc5be96d2a0ef9ca02189f8a115fa6838303.png",
            imageAlt: "三分之一",
            text: "剩余10万+份"
          })}
        </div>
        <div 
          className={`${styles.middlePoint} ${clickedPoints.middle ? styles.clicked : ''}`}
          onClick={() => handlePointClick('middle')}
        >
          {renderImageWithText({
            imageSrc: "https://p0.meituan.net/cagent/29cf01194ce0d24d799407aacf0d4db421179.png",
            imageAlt: "三分之二",
            text: "剩余10万+份"
          })}
        </div>
        <div 
          className={`${styles.endPoint} ${clickedPoints.end ? styles.clicked : ''}`}
          onClick={() => handlePointClick('end')}
        >
          {renderImageWithText({
            imageSrc: "https://p0.meituan.net/cagent/08cb643f4478eb22e91486846e4a436f10224.png",
            imageAlt: "终点",
            text: "剩余10万+份"
          })}
        </div>
      </div>
      <div className={styles.progressContainer}>
        <Progress
          percent={progress}
          size={[357, 6]}
          strokeColor={colors}
          trailColor="#F4FFA3"
          showInfo={false}
        />
        <div className={styles.iconContainer}>
          <div 
            className={`${styles.thirdPoint} ${clickedPoints.third ? styles.clicked : ''}`}
            onClick={() => handlePointClick('third')}
          >
            <img
              src="https://p0.meituan.net/cagent/9e20d04e0c93a3fdc3632935a792c2411767.png"
              alt="三分之一"
              //loading="lazy"
            />
          </div>
          <div 
            className={`${styles.middlePoint} ${clickedPoints.middle ? styles.clicked : ''}`}
            onClick={() => handlePointClick('middle')}
          >
            <img
              src="https://p0.meituan.net/cagent/9e20d04e0c93a3fdc3632935a792c2411767.png"
              alt="三分之二"
              //loading="lazy"
            />
          </div>
          <div 
            className={`${styles.endPoint} ${clickedPoints.end ? styles.clicked : ''}`}
            onClick={() => handlePointClick('end')}
          >
            <img
              src="https://p0.meituan.net/cagent/9e20d04e0c93a3fdc3632935a792c2411767.png"
              alt="终点"
              //loading="lazy"
            />
          </div>
        </div>
      </div>
      <div className={styles.textContainer}>
        <div className={styles.thirdPoint}>
          <span>集齐1套</span>
        </div>
        <div className={styles.middlePoint}>
          <span>集齐2套</span>
        </div>
        <div className={styles.endPoint}>
          <span>集齐3套</span>
        </div>
      </div>
    </div>
  );
}

export default RewardProcess;
