@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: fixed;
  z-index: 997;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0);
}

/* lottie 动画容器：居中定位，层级位于 overlay 与 box 之间 */
.lottie-container {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 998;
  width: 100vw;
  height: auto;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.box {
  position: fixed;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  overflow: hidden;

  &-title {
    width: 100%;
    font-weight: 400;
    font-size: 33px;
    font-family: "MF YuanHei", sans-serif;
    font-style: italic;
    line-height: 33px;
    letter-spacing: 2px;
    text-align: center;
    background: linear-gradient(175deg, #FFFDF2 28%, #FFF5B2 77%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    flex-shrink: 0;
  }

  &-subtitle {
    width: 100%;
    color: #fff;
    font-weight: 400;
    font-size: 22px;
    font-family: "MF YuanHei", sans-serif;
    line-height: 33px;
    letter-spacing: 2px;
    text-align: center;
    flex-shrink: 0;

    &-number {
      margin: 0 4px;
      font-weight: 700;
      font-size: 22px;
      font-family: "Meituan Type-Regular", sans-serif;
      font-variation-settings: "opsz" auto;
    }
  }

  &-card {
    @include flex-center;

    width: auto;
    max-width: 70%; /* 进一步缩小卡片宽度 */
    flex: 0 1 auto; /* 改为不伸缩，让卡片保持自然大小 */
    min-height: 0;
    position: relative;
    margin-bottom: 12px; /* 减少卡片与按钮间距 */

    &-image {
      width: auto;
      max-width: 100%;
      height: auto;
      max-height: 100%;
      object-fit: contain;
      object-position: center;
    }

    .hidden-tag-image {
      position: absolute;
      top: 5%;
      right: -24px;
      width: 78px;
      height: 78px;
      z-index: 2;
      overflow: visible;
    }
  }

  /* 按钮组：横向排列分享和继续按钮 */
  &-btn-group {
    @include flex-center;

    flex-direction: row;
    gap: 20px;
    width: 100%;
    height: 50px;
    flex-shrink: 0;
  }

  /* 外层：分享按钮边框 */
  &-share-wrapper {
    @include flex-center;
    width: 140px;
    height: 100%;
    border: 1px solid #E9FB61;
    border-radius: 9999px;
  }

  &-share-btn {
    width: 100%;
    height: 100%;
    color: #E7FA5C;
    font-weight: 600;
    font-size: 17px;
    font-family: "PingFang SC", sans-serif;
    line-height: 17px;
    letter-spacing: 0;
    text-align: center;
    background: transparent;
    border-radius: 9999px;
  }
  &-continue-btn {
    width: 170px;
    height: 50px; /* 明确设置高度为50px，与按钮组高度一致 */
    color: #5F6720;
    font-weight: 500;
    font-size: 20px;
    font-family: "PingFang SC", sans-serif;
    line-height: 20px;
    letter-spacing: 0;
    text-align: center;
    background: linear-gradient(93deg, #E7FA55 3%, #F4FFA3 85%);
    border-radius: 9999px;
    position: relative;
    border: none; /* 移除默认边框 */
    outline: none; /* 移除默认轮廓 */
    
    /* 使用伪元素创建渐变边框 */
    &::before {
      content: '';
      position: absolute;
      top: -2.54px;
      left: -2.54px;
      right: -2.54px;
      bottom: -2.54px;
      background: linear-gradient(179deg, #FBFFDE 32%, #E1FF00 102%);
      border-radius: 9999px;
      z-index: -1;
    }
    
    /* 确保按钮内容完全填充 */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-close {
    @include flex-center;

    width: 30px;
    height: 30px;
    margin-top: 18px;
    &-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      pointer-events: none;
    }
  }
}

/* Loading 遮罩层样式 */
.loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}