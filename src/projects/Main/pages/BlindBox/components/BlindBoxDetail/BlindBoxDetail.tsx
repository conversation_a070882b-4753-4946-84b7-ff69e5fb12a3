import React, { useState, useRef, useEffect } from "react";

import styles from "./index.module.scss";
import { isNotEmptyString } from "@/projects/Main/utils/stringUtil";
import { Image, Toast, Loading } from "@roo/roo-b-mobile";
import ShareDialog from "../ShareDialog/ShareDialog";
import { motion, AnimatePresence } from "framer-motion";
// import collectionAnimationData from "@/assets/lottie/collection-popup-lottie.json";
import {
  reportBlindBoxDetailMv,
  reportBlindBoxDetailMc,
} from "../../constants/report";
import {
  CardType,
  CollectionStatus,
  BlindBoxDetailMode,
} from "../../constants/BlindBoxConstants";
import { getLottieAssetUrl } from "@/projects/Main/utils/assetUtil";
import {
  fetchGiveCard,
  fetchCancelGiveCard,
  fetchReceiveCard,
  IGiveCardResult,
} from "../../../../services/blindbox/fetchBlindBox";
import { appShareH5 } from "@/projects/Main/utils/shareUtil";
import { addUrlParam } from "@/constants/scheme";
import { usePreloadLottieData, useLottieAnimation } from "@/hooks/use-lottie-animation";

interface BlindBoxDetailProps {
  img: string | undefined; // 展示兜底图
  shareImg: string | undefined;
  shareOriginImg: string | undefined;
  spuName: string;
  collectedCount: number;
  totalCount: number;
  cardId: number; // 卡牌ID，用于埋点
  cardType: number; // 卡牌类型，用于判断是否为隐藏款
  cardStatus: number; // 卡片状态
  giftToken?: string | null; // 礼品令牌，用于取消赠送
  giverUserName?: string; // 赠送者用户名，可选参数

  mode: BlindBoxDetailMode; // 弹窗模式
  isLast?: boolean; // 是否为最后一张（仅在NEWLY_ACQUIRED模式下使用）
  handleClose: () => void;
  onNext?: () => void; // 下一个卡牌的回调（仅在NEWLY_ACQUIRED模式下使用）
  onRefresh?: () => void; // 刷新数据的回调
}

function BlindBoxDetail(props: BlindBoxDetailProps) {
  const {
    spuName,
    collectedCount,
    totalCount,
    shareImg,
    shareOriginImg,
    img,
    cardId,
    cardType,
    cardStatus,
    giftToken,
    giverUserName,
    handleClose,
    mode,
    isLast = false,
    onNext,
    onRefresh,
  } = props;
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [showShareDialog, setShowShareDialog] = useState<boolean>(false);
  const [shouldPlayAnimation, setShouldPlayAnimation] =
    useState<boolean>(false);
  const [contentAnimationKey, setContentAnimationKey] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  // 判断是否为隐藏款
  const isHidden = cardType === CardType.MYSTERY;

  // 预加载lottie动画数据
  const collectionAnimationUrl = getLottieAssetUrl("COLLECTION_POPUP");
  const { loadedData, isLoading, error } = usePreloadLottieData([
    collectionAnimationUrl
  ]);

  // 使用通用动画hook
  const collectionAnimation = useLottieAnimation();

  // 根据模式获取按钮文案和回调
  const getButtonConfig = () => {
    switch (mode) {
      case BlindBoxDetailMode.NEWLY_ACQUIRED:
        return {
          title: `\u00A0获得「${spuName}」`,
          text: onNext && !isLast ? "下一个" : "继续",
          onClick: handleContinueClick,
        };
      case BlindBoxDetailMode.RECEIVE:
        return {
          title: `${giverUserName || '好友'}赠送给你${spuName}`,
          text: "接收",
          onClick: handleReceiveClick,
        };
      case BlindBoxDetailMode.NORMAL:
        return {
          title: `\u00A0获得「${spuName}」`,
          text:
            cardStatus === CollectionStatus.PENDING_CLAIM ? "取消赠送" : "赠送",
          onClick:
            cardStatus === CollectionStatus.PENDING_CLAIM
              ? handleCancelGiveClick
              : handleGiveClick,
        };
      default:
        return {
          title: `\u00A0获得「${spuName}」`,
          text: "继续",
          onClick: handleContinueClick,
        };
    }
  };



  // 页面曝光埋点
  useEffect(() => {
    reportBlindBoxDetailMv();
  }, []);

  /**
   * 初始化并播放 lottie 动画
   * 在组件挂载时加载动画，在卸载时销毁，避免内存泄漏
   */
  useEffect(() => {
    if (!collectionAnimation.animationRef.current || isLoading || error) return;

    // 使用预加载的数据
    const collectionAnimationData = loadedData.get(collectionAnimationUrl);
    if (collectionAnimationData) {
      console.log('[DEBUG] 使用预加载的收藏动画数据');
      collectionAnimation.playAnimation(collectionAnimationData);
    } else {
      console.warn('[WARN] 收藏动画数据未预加载，使用备用加载方式');
      // 备用加载方式
      collectionAnimation.loadLottieData(collectionAnimationUrl).then((data) => {
        collectionAnimation.playAnimation(data);
      });
    }
  }, [isLoading, error, loadedData, collectionAnimationUrl, collectionAnimation]);

  /**
   * 监听shouldPlayAnimation状态变化，播放动画
   */
  useEffect(() => {
    if (shouldPlayAnimation && collectionAnimation.animationRef.current) {
      // 使用通用hook的播放方法
      const collectionAnimationData = loadedData.get(collectionAnimationUrl);
      if (collectionAnimationData) {
        collectionAnimation.playAnimation(collectionAnimationData);
      }
      setShouldPlayAnimation(false);
    }
  }, [shouldPlayAnimation, loadedData, collectionAnimationUrl, collectionAnimation]);

  const handleShareClick = () => {
    // 触发分享按钮点击埋点
    reportBlindBoxDetailMc("分享", cardId, collectedCount);
    setShowShareDialog(true);
  };

  const handleContinueClick = () => {
    // 触发继续/下一个按钮点击埋点
    reportBlindBoxDetailMc(buttonConfig.text, cardId, collectedCount);

    if (mode === BlindBoxDetailMode.NEWLY_ACQUIRED && onNext && !isLast) {
      // 如果是新获得卡牌且有下一个回调，先调用onNext传入新数据
      onNext();
      // 设置标志位，在下一个useEffect中播放动画
      setShouldPlayAnimation(true);
      // 触发弹窗内容动画重新播放
      setContentAnimationKey((prev) => prev + 1);
    } else {
      // 否则关闭弹窗
      setIsOpen(false);
    }
  };

  // 赠送
  const handleGiveClick = async () => {
    try {
      setLoading(true);
      const result = await fetchGiveCard(cardId);

      if (result.canGive) {
        // 赠送成功，调用分享功能
        handleGive(result.giftUrl, result.giftToken);
      } else {
        // 不能赠送，显示具体原因
        Toast.open({
          content: result.cannotGiveReason || "无法赠送，请稍后再试",
        });
      }
    } catch (error) {
      console.error("赠送卡牌失败:", error);
      // 错误处理已在 fetchGiveCard 中统一处理，这里不再重复显示 Toast
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理分享成功逻辑
   * @param giftUrl 礼品分享链接
   * @param newGiftToken 新的礼品令牌
   */
  const handleGive = (giftUrl?: string | null, newGiftToken?: string | null) => {
    // 优先使用API返回的giftUrl，如果没有则使用默认的URL参数处理方式
    const shareUrl = giftUrl || addUrlParam(window.location.href, 'gift_token', newGiftToken || giftToken || '');
    console.log("handleShare url:", shareUrl);
    appShareH5({
      title: "不知道喝啥，抽个盲盒试试！",
      desc: "看看你今天的幸运奶茶是什么？",
      image:
        "https://p0.meituan.net/cagent/847e1080198c3a2d1334b29487372ef218374.png",
      url: shareUrl,
      onSuccess: () => {
        // 分享成功后刷新数据
        onRefresh && onRefresh();
      },
    });
  };

  // 取消赠送
  const handleCancelGiveClick = async () => {
    try {
      setLoading(true);

      // 检查是否有 giftToken
      if (!giftToken) {
        Toast.open({
          content: "无法取消赠送，缺少礼品令牌",
        });
        return;
      }

      const result = await fetchCancelGiveCard(cardId, giftToken);

      if (result.cancelSuccess) {
        Toast.open({
          content: "取消赠送成功",
        });
        // 取消成功后刷新数据并关闭弹窗
        onRefresh && onRefresh();
        setIsOpen(false);
      } else {
        Toast.open({
          content: result.cannotCancelReason || "取消赠送失败",
        });
      }
    } catch (error) {
      console.error("取消赠送失败:", error);
      // 错误处理已在 fetchCancelGiveCard 中统一处理，这里不再重复显示 Toast
    } finally {
      setLoading(false);
    }
  };

  // 接收赠送
  const handleReceiveClick = async () => {
    try {
      setLoading(true);

      // 检查是否有 giftToken
      if (!giftToken) {
        Toast.open({
          content: "无法接收赠送，缺少礼品令牌",
        });
        return;
      }

      const result = await fetchReceiveCard(cardId, giftToken);

      if (result.cardReceived) {
        Toast.open({
          content: "接收成功",
        });
        onRefresh && onRefresh();
       
        
      } else {
        Toast.open({
          content: result.cannotReceiveReason || "接收失败",
        });
      }
       // 接收成功后延迟关闭弹窗
      setTimeout(() => {
        setIsOpen(false);
      }, 2000);
    } catch (error) {
      console.error("接收卡牌失败:", error);
      // 错误处理已在 fetchReceiveCard 中统一处理，这里不再重复显示 Toast
    } finally {
      setLoading(false);
    }
  };

  const handleCloseClick = () => {
    // 触发关闭按钮点击埋点
    reportBlindBoxDetailMc("关闭", cardId, collectedCount);
    setIsOpen(false);
  };

  // 根据模式获取按钮文案和回调
  const buttonConfig = getButtonConfig();

  // 蒙层动画配置
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 0.85,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  // 弹窗内容动画配置 - 弹簧效果
  const contentVariants = {
    hidden: { scale: 0.4, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        damping: 5, // 摩擦力
        stiffness: 20, // 张力
        mass: 0.5,
      },
    },
    exit: {
      scale: 0.8,
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  return (
    <AnimatePresence
      onExitComplete={() => {
        handleClose();
      }}
    >
      {isOpen && (
        <>
          <motion.div
            className={styles.overlay}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={overlayVariants}
          />
          {/* lottie 动画容器：位于 overlay 与弹窗之间 */}
          <div
            ref={collectionAnimation.animationRef}
            className={styles["lottie-container"]}
          />
          <motion.div
            className={styles.box}
            key={contentAnimationKey}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
          >
            <div>
              <div className={styles.boxTitle}>
                {buttonConfig.title}
              </div>
              <div className={styles.boxSubtitle}>
                已收集
                <span className={styles.boxSubtitleNumber}>
                  {collectedCount}/{totalCount}
                </span>
                款灵感盲盒
              </div>
            </div>

            <div className={styles.boxCard}>
              {isNotEmptyString(img) ? (
                <img className={styles.boxCardImage} src={img} alt="card" />
              ) : null}
              {/* 右上角隐藏款图片标签 */}
              {isHidden && (
                <img
                  src="https://p0.meituan.net/cagent/155fd14a2fa1cde6466901d3cd7712cf14962.png"
                  alt="隐藏款"
                  className={styles.hiddenTagImage}
                  //loading="lazy"
                />
              )}
            </div>
            <div className={styles.boxBtnGroup}>
              {/* 分享按钮：外层边框 + 内层按钮 - 在RECEIVE模式下不展示 */}
              {mode !== BlindBoxDetailMode.RECEIVE && (
                <div className={styles.boxShareWrapper}>
                  <button
                    className={styles.boxShareBtn}
                    onClick={handleShareClick}
                    type="button"
                    disabled={loading}
                  >
                    分享
                  </button>
                </div>
              )}

              {/* 主要操作按钮：根据模式显示不同的文案和回调 */}
              <button
                className={styles.boxContinueBtn}
                onClick={buttonConfig.onClick}
                type="button"
                disabled={loading}
              >
                {buttonConfig.text}
              </button>
            </div>
            <div
              className={styles.boxClose}
              aria-label="关闭"
              onClick={handleCloseClick}
            >
              <Image
                className={styles.boxCloseImage}
                src="https://p0.meituan.net/cagent/a629fb6be7984c646ac129207362a0111361.png"
                alt="关闭"
              />
            </div>
            {showShareDialog && (
              <ShareDialog
                open={showShareDialog}
                shareImg={shareImg}
                shareOriginImg={shareOriginImg}
                spuName={spuName}
                content={"看看你今天的幸运奶茶是什么？"}
                cardId={cardId}
                handleClose={function (): void {
                  setShowShareDialog(false);
                }}
              />
            )}
          </motion.div>

          {/* Loading 遮罩层 */}
          {loading && (
            <div className={styles.loadingOverlay}>
              <Loading spinning iconType="vline" />
            </div>
          )}
        </>
      )}
    </AnimatePresence>
  );
}

export default BlindBoxDetail;
