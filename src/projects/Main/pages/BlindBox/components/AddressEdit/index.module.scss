.addressEdit {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px; // 添加input之间的间距
}

// 提示文字样式
.tipText {
  font-family: PingFang SC;
  font-size: 11px;
  font-weight: normal;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #888888;
  margin-bottom: 8px;
}

// 输入框样式
.inputField {
  width: 100%;
  border-radius: 661px;
  background: rgba(0, 0, 0, 0.04);
  border: none;
  outline: none;
  padding: 12px 16px;
  font-size: 16px;
  color: #000000;
  
  // 占位符文字样式
  &::placeholder {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: 0px;
    color: #C2C2C2;
  }
  
  // 聚焦时的样式
  &:focus {
    background: rgba(0, 0, 0, 0.06);
  }
}

// 地址输入框特殊样式 - 文本溢出省略号
.addressInput {
  @extend .inputField;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 级联选择容器
.cascadeSelectContainer {
  position: relative;
  margin-bottom: 8px;
}

// 级联选择框样式
.cascadeSelect {
  width: 100%;
  border-radius: 661px;
  background: rgba(0, 0, 0, 0.04);
  border: none;
  outline: none;
  padding: 12px 16px;
  font-size: 16px;
  color: #000000;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  // 禁用状态样式
  &:disabled {
    background: rgba(0, 0, 0, 0.02);
    color: #C2C2C2;
    cursor: not-allowed;
  }
  
  // 聚焦时的样式
  &:focus {
    background: rgba(0, 0, 0, 0.06);
  }
}

.cascadeText {
  flex: 1;
  text-align: left;
}

.cascadeArrow {
  font-size: 12px;
  color: #888888;
  transition: transform 0.2s ease;
}

// 级联下拉框样式
.cascadeDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  display: flex;
}

.cascadeLevel {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  
  &:last-child {
    border-right: none;
  }
}

.cascadeTitle {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  background: #f8f8f8;
  border-bottom: 1px solid #f0f0f0;
}

.cascadeOption {
  padding: 12px 16px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.selected {
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
  }
}

// Toast内容样式
.toastContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.toastTitle {
  font-weight: bold;
}

.toastMessage {
  font-size: 14px;
}

// 全局级联下拉框样式
.globalCascadeDropdown {
  position: fixed;
  z-index: 99999;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 300px;
  overflow: hidden;
  display: flex;
  min-height: 200px;
}

// 级联列样式
.cascadeColumn {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  background-color: #f8f8f8;
  min-width: 100px;

  &:last-child {
    border-right: none;
  }
}

// 级联标题样式
.cascadeTitle {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  background-color: #f8f8f8;
  border-bottom: 1px solid #f0f0f0;
}

// 级联选项样式
.cascadeOption {
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  background-color: transparent;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
  -webkit-user-select: none;
  user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

