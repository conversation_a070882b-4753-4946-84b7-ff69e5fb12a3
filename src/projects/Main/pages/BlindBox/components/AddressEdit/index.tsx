import { Dialog, Toast } from '@roo/roo-b-mobile';
import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { ADDRESS_DATA } from '@/projects/Main/constants/address';
import styles from './index.module.scss';

interface AddressEditProps {
  visible?: boolean;
  initialData?: AddressData;
  editable?: boolean; // 新增：是否可修改
  onOk?: (addressData: AddressData) => void;
  onCancel?: () => void;
}

interface AddressData {
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  address: string;
}

const AddressEdit: React.FC<AddressEditProps> = ({
  visible = false,
  initialData,
  editable = true, // 新增：是否可修改，默认为true
  onOk,
  onCancel,
}) => {
  const [addressData, setAddressData] = useState<AddressData>(
    initialData || {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      address: '',
    },
  );

  // 级联选择状态
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedCity, setSelectedCity] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [showCascadeSelect, setShowCascadeSelect] = useState<boolean>(false);
  const cascadeSelectRef = React.useRef<HTMLDivElement>(null);
  const cascadeDropdownRef = React.useRef<HTMLDivElement>(null);
  const [cascadePosition, setCascadePosition] = useState<{
    top: number;
    left: number;
    width: number;
  }>({ top: 0, left: 0, width: 0 });

  // 当initialData变化时，更新addressData
  React.useEffect(() => {
    if (initialData) {
      setAddressData(initialData);
      setSelectedProvince(initialData.province);
      setSelectedCity(initialData.city);
      setSelectedDistrict(initialData.district);
    }
  }, [initialData]);

  // 点击外部关闭下拉框
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // 检查是否点击了级联选择器或级联下拉框
      if (cascadeSelectRef.current?.contains(target) || cascadeDropdownRef.current?.contains(target)) {
        return; // 如果点击的是级联选择器或下拉框内部，不关闭
      }

      setShowCascadeSelect(false);
    };

    if (showCascadeSelect) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showCascadeSelect]);

  const handleInputChange = (field: keyof AddressData, value: string) => {
    // 如果不可编辑，不允许修改
    if (!editable) {
      return;
    }
    setAddressData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 处理级联选择器点击
  const handleCascadeClick = () => {
    if (!editable) return;

    if (cascadeSelectRef.current) {
      const rect = cascadeSelectRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // 计算最佳位置，避免超出屏幕边界
      let top = rect.bottom;
      let left = rect.left;

      // 如果下方空间不够，显示在上方
      if (rect.bottom + 300 > viewportHeight) {
        top = Math.max(0, rect.top - 300);
      }

      // 如果右侧空间不够，调整左边距
      if (rect.left + rect.width > viewportWidth) {
        left = Math.max(0, viewportWidth - rect.width - 10);
      }

      // 确保最小宽度
      const minWidth = 200;
      const width = Math.max(rect.width, minWidth);

      const position = {
        top,
        left,
        width,
      };
      setCascadePosition(position);
    }
    setShowCascadeSelect(!showCascadeSelect);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const handleOk = () => {
    Toast.open({
      className: '123',
      content: (
        <div className={styles.toastContent}>
          <div className={styles.toastTitle}>兑换成功</div>
          <div className={styles.toastMessage}>礼品将于活动结束后30天内发货，请耐心等待</div>
        </div>
      ),
      icon: 'success',
      maxLines: -1,
      afterClose: () => {
        console.log('afterClose');
      },
    });
    // 验证必填字段
    if (!addressData.name.trim()) {
      Toast.open({
        content: '请输入收件人',
      });
      return;
    }

    if (!addressData.phone.trim()) {
      Toast.open({
        content: '请输入联系电话',
      });
      return;
    }

    // 验证手机号格式
    if (!/^\d{11}$/.test(addressData.phone.trim())) {
      Toast.open({
        content: '请输入正确的联系电话',
      });
      return;
    }

    if (!addressData.province) {
      Toast.open({
        content: '请选择省市区',
      });
      return;
    }

    if (!addressData.city) {
      Toast.open({
        content: '请选择城市',
      });
      return;
    }

    if (!addressData.district) {
      Toast.open({
        content: '请选择区县',
      });
      return;
    }

    if (!addressData.address.trim()) {
      Toast.open({
        content: '请输入详细地址',
      });
      return;
    }

    // 验证详细地址长度
    const addressLength = addressData.address.trim().length;
    if (addressLength < 5 || addressLength > 50) {
      Toast.open({
        content: '详细地址最少5个字，最多50个字',
      });
      return;
    }

    if (onOk) {
      onOk(addressData);
    } else {
      // 默认保存逻辑
      Toast.open({
        content: '地址保存成功',
      });
    }
  };

  // 处理级联选择
  const handleCascadeSelect = (level: number, value: string, label: string) => {
    if (!editable) return;

    if (level === 1) {
      // 选择省份
      setSelectedProvince(value);
      setSelectedCity('');
      setSelectedDistrict('');
      setAddressData((prev) => ({
        ...prev,
        province: value,
        city: '',
        district: '',
      }));
    } else if (level === 2) {
      // 选择城市
      setSelectedCity(value);
      setSelectedDistrict('');
      setAddressData((prev) => ({
        ...prev,
        city: value,
        district: '',
      }));
    } else if (level === 3) {
      // 选择区县
      setSelectedDistrict(value);
      setAddressData((prev) => ({
        ...prev,
        district: value,
      }));
      setShowCascadeSelect(false);
    }
  };

  // 获取当前选择的显示文本
  const getCascadeDisplayText = () => {
    if (!selectedProvince) return '请选择省市区';

    const province = ADDRESS_DATA.find((p) => p.value === selectedProvince);
    if (!province) return '请选择省市区';

    const city = province.children?.find((c) => c.value === selectedCity);
    if (!city) return province.label;

    const district = city.children?.find((d) => d.value === selectedDistrict);
    if (!district) return `${province.label} ${city.label}`;

    return `${province.label} ${city.label} ${district.label}`;
  };

  // 级联下拉框组件
  const CascadeDropdown = () => {
    if (!showCascadeSelect) return null;

    return (
      <div
        ref={cascadeDropdownRef}
        className={styles.globalCascadeDropdown}
        style={{
          top: cascadePosition.top,
          left: cascadePosition.left,
          width: cascadePosition.width,
        }}
      >
        {/* 省份选择 */}
        <div className={styles.cascadeColumn}>
          <div className={styles.cascadeTitle}>省份</div>
          {ADDRESS_DATA.map((province) => (
            <div
              key={province.value}
              className={`${styles.cascadeOption} ${selectedProvince === province.value ? styles.selected : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                handleCascadeSelect(1, province.value, province.label);
              }}
            >
              {province.label}
            </div>
          ))}
        </div>

        {/* 城市选择 */}
        {selectedProvince && (
          <div className={styles.cascadeColumn}>
            <div className={styles.cascadeTitle}>城市</div>
            {ADDRESS_DATA.find((p) => p.value === selectedProvince)?.children?.map((city) => (
              <div
                key={city.value}
                className={`${styles.cascadeOption} ${selectedCity === city.value ? styles.selected : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCascadeSelect(2, city.value, city.label);
                }}
              >
                {city.label}
              </div>
            ))}
          </div>
        )}

        {/* 区县选择 */}
        {selectedCity && (
          <div className={styles.cascadeColumn}>
            <div className={styles.cascadeTitle}>区县</div>
            {ADDRESS_DATA.find((p) => p.value === selectedProvince)
              ?.children?.find((c) => c.value === selectedCity)
              ?.children?.map((district) => (
                <div
                  key={district.value}
                  className={`${styles.cascadeOption} ${selectedDistrict === district.value ? styles.selected : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCascadeSelect(3, district.value, district.label);
                  }}
                >
                  {district.label}
                </div>
              ))}
          </div>
        )}
      </div>
    );
  };

  const dialogContent = (
    <div className={styles.addressEdit}>
      <div className={styles.tipText}>收货地址仅可填写一次，且不支持修改，请谨慎提交~</div>
      <input
        placeholder="请填写收货人姓名"
        className={styles.inputField}
        value={addressData.name}
        onChange={(e) => handleInputChange('name', e.target.value)}
        readOnly={!editable}
      />
      <input
        placeholder="请填写收货人联系方式"
        className={styles.inputField}
        value={addressData.phone}
        onChange={(e) => handleInputChange('phone', e.target.value)}
        readOnly={!editable}
      />

      {/* 级联省市区选择 */}
      <div className={styles.cascadeSelectContainer} ref={cascadeSelectRef}>
        <div className={styles.cascadeSelect} onClick={handleCascadeClick}>
          <span className={styles.cascadeText}>{getCascadeDisplayText()}</span>
          <span className={styles.cascadeArrow}>{showCascadeSelect ? '▲' : '▼'}</span>
        </div>
      </div>

      <input
        placeholder="请填写详细地址"
        className={styles.addressInput}
        value={addressData.address}
        onChange={(e) => handleInputChange('address', e.target.value)}
        readOnly={!editable}
      />
    </div>
  );

  return (
    <>
      <Dialog
        open={visible}
        onOk={editable ? handleOk : undefined}
        onCancel={handleCancel}
        cancelText={editable ? '关闭' : '退出'}
        okText={editable ? '确认' : undefined}
        title="收货地址"
        content={dialogContent}
        destroyOnClose
        //position="bottom"
      />
      {/* 全局级联下拉框 */}
      {showCascadeSelect && createPortal(<CascadeDropdown />, document.body)}
    </>
  );
};

export default AddressEdit;
