.box {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  cursor: pointer;
  align-items: center;

  &-wrapper {
    position: relative;
    width: 100%;
    aspect-ratio: 192 / 252;
  }

  &-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: fill;
    z-index: 1;
  }

  &-content {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, calc(-50% + 3px));
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 2;

    &.boxContentClosed {
      width: 70%;
      height: 70%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  &-nameContainer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: auto;
    object-fit: contain;
    z-index: 3;
  }

  &-contentAnim {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 35%;
    height: 35%;
    object-fit: contain;
    z-index: 3;
  }

  &-bubble {
    position: absolute;
    top: -2px;
    right: -12px;
    width: 64px;
    height: 23px;
    z-index: 1;
  }
}

.lottieContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  pointer-events: none;
}

.cardName {
  position: absolute;
  bottom: 0;
  left: 25%;
  transform: translateX(-50%);
  opacity: 0.8;
  font-family: FZLanTingYuanS-R-GB;
  font-size: 10px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #3B4106;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 50%;
  z-index: 3;
}

.sweepLightContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}
