import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import styles from "./index.module.scss";
import { IDrawnCard } from "@/projects/Main/services/blindbox/fetchBlindBox";
import { CollectionStatus } from "../../constants/BlindBoxConstants";
import { useLottieAnimation } from "@/hooks/use-lottie-animation";
import { getLottieAssetUrl } from "@/projects/Main/utils/assetUtil";

interface SelectBoxItemProps {
  handleBoxClick: () => void;
  cardName: string;
  imageUrl: string;
  drawStatus: number;
  isOpen: boolean;
  isClosingAnimation?: boolean;
  onAnimationComplete?: () => void; // 动画完成回调
  preloadedLottieData?: any; // 预加载的lottie数据
  isLottieLoaded?: boolean; // lottie数据是否已加载
}

function SelectBoxItem(props: SelectBoxItemProps) {
  const {
    handleBoxClick,
    cardName,
    imageUrl,
    drawStatus,
    isOpen,
    isClosingAnimation = false,
    onAnimationComplete,
    preloadedLottieData,
    isLottieLoaded = false,
  } = props;

  // 使用通用动画hook
  const openAnimation = useLottieAnimation();
  const sweepLightAnimation = useLottieAnimation();

  const hasPlayedRef = useRef(false);
  const hasSweepLightPlayedRef = useRef(false);
  const [showSweepLight, setShowSweepLight] = useState<boolean>(false);
  const [animationPhase, setAnimationPhase] = useState<
    "normal" | "shrinking" | "expanding"
  >("normal");

  // 缓存优化后的Lottie数据
  const optimizedLottieDataRef = useRef<any>(null);

  // 创建优化后的Lottie数据
  const createOptimizedLottieData = (lottieData: any) => {
    return {
      ...lottieData,
      fr: 50,
      ip: 50,
      op: 121,
    };
  };

  // 加载Lottie数据的公共方法
  const loadLottieData = (animationHook: any, onLoaded: (lottieData: any) => void) => {
    if (isLottieLoaded && preloadedLottieData) {
      // 使用预加载的数据
      onLoaded(preloadedLottieData);
    } else {
      // 加载Lottie数据
      const lottieDataUrl = getLottieAssetUrl('SELECT_BOX_ITEM');
      animationHook.loadLottieData(lottieDataUrl).then(onLoaded);
    }
  };

  // 播放扫光动画的公共方法
  const playSweepLightAnimation = (onComplete?: () => void) => {
    if (!sweepLightAnimation.animationRef.current) {
      console.warn("扫光动画容器未准备好");
      setShowSweepLight(false);
      onComplete?.();
      return;
    }

    const playAnimation = (lottieData: any) => {
      const optimizedData = createOptimizedLottieData(lottieData);
      optimizedLottieDataRef.current = optimizedData;
      
      sweepLightAnimation.playAnimation(optimizedData, () => {
        setShowSweepLight(false);
        onComplete?.();
      });
    };

    loadLottieData(sweepLightAnimation, playAnimation);
  };

  // 播放盲盒打开动画的公共方法
  const playOpenAnimation = () => {
    const playAnimation = (lottieData: any) => {
      openAnimation.playAnimation(lottieData);
      hasPlayedRef.current = true;
    };

    loadLottieData(openAnimation, playAnimation);
  };

  // 处理盲盒打开动画
  useEffect(() => {
    if (isOpen && !hasPlayedRef.current) {
      playOpenAnimation();
    }
  }, [isOpen, openAnimation, isLottieLoaded, preloadedLottieData]);

  // 组件加载时播放扫光动画
  useEffect(() => {
    if (!hasSweepLightPlayedRef.current) {
      hasSweepLightPlayedRef.current = true;
      setShowSweepLight(true);

      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        playSweepLightAnimation();
      }, 100);
    }
  }, [sweepLightAnimation, isLottieLoaded, preloadedLottieData]);

  // 处理关闭动画序列和扫光动画
  useEffect(() => {
    if (isClosingAnimation && animationPhase === "normal") {
      console.log("开始关闭动画序列");
      // 开始缩小阶段
      setAnimationPhase("shrinking");

      // 缩小完成后开始放大阶段，并在放大完成后立即播放扫光动画
      setTimeout(() => {
        setAnimationPhase("expanding");

        // 放大动画完成后立即播放扫光动画
        setShowSweepLight(true);
        playSweepLightAnimation(() => {
          // 动画序列完成，调用回调
          onAnimationComplete?.();
        });
      }, 150); // ease out动画持续时间
    }
  }, [isClosingAnimation, animationPhase, sweepLightAnimation, isLottieLoaded, preloadedLottieData]);

  // 当isClosingAnimation变为false时，重置动画状态
  useEffect(() => {
    if (!isClosingAnimation && animationPhase !== "normal") {
      console.log("重置动画状态");
      setAnimationPhase("normal");
    }
  }, [isClosingAnimation, animationPhase]);

  // 计算当前动画值
  const getAnimationValues = () => {
    if (animationPhase === "shrinking") {
      return {
        scale: 0.5,
        opacity: 0,
      };
    } else if (animationPhase === "expanding") {
      return {
        scale: 1,
        opacity: 1,
      };
    }
    return {
      scale: 1,
      opacity: 1,
    };
  };

  // 计算当前过渡配置
  const getTransitionConfig = () => {
    if (animationPhase === "shrinking") {
      return {
        duration: 0.15,
        ease: "easeOut",
      };
    } else if (animationPhase === "expanding") {
      return {
        type: "spring",
        stiffness: 20,
        damping: 5,
        scale: {
          from: 0.5,
          to: 1,
        },
      };
    }
    return {};
  };

  const handleClick = () => {
    // 只有在非动画播放状态下才允许点击
    handleBoxClick?.();
  };

  return (
    <motion.div
      className={styles.box}
      onClick={handleClick}
      animate={getAnimationValues()}
      transition={getTransitionConfig()}
    >
      <div className={styles.boxWrapper}>
        <img
          className={styles.boxContainer}
          src={
            "https://p0.meituan.net/cagent/5b08bf0c3732fd46208e2e05490dc4a89994.png"
          }
          alt="box"
          //loading="lazy"
        />
        <img
          className={styles.boxNameContainer}
          src={
            "https://p1.meituan.net/cagent/75f0012a23462abbd53dba245f42d5292855.png"
          }
          alt={cardName || "content"}
          //loading="lazy"
        />
        {imageUrl && (
          <img
            className={`${styles.boxContent} ${!isOpen ? styles.boxContentClosed : ""}`}
            src={imageUrl}
            alt={cardName || "content"}
            //loading="lazy"
          />
        )}
        {!isOpen && (
          <img
            className={styles.boxContentAnim}
            src={
              "https://p0.meituan.net/cubeforwebp/a9e8bf4b0abc28cb54dcab693bdb3238142184.webp"
            }
            alt="box animation"
            //loading="lazy"
          />
        )}
        {/* Lottie动画容器 */}
        {/* {isOpen && (
          <div
            ref={openAnimation.animationRef}
            className={styles.lottieContainer}
          />
        )} */}
        <div
          className={styles.cardName}
          style={{ color: isOpen ? "#3B4106" : "#83901E" }}
        >
          {cardName}
        </div>
      </div>
      {drawStatus === CollectionStatus.DRAWN && (
        <img
          src="https://p0.meituan.net/cagent/f1cee5c106ef4e0e1efe4252e083508119863.png"
          alt="bubble"
          className={styles.boxBubble}
          //loading="lazy"
        />
      )}
      {/* 扫光效果 - 始终渲染容器，通过opacity控制显示 */}
      <div
        ref={sweepLightAnimation.animationRef}
        className={styles.sweepLightContainer}
        style={{
          opacity: showSweepLight ? 1 : 0,
          pointerEvents: "none",
        }}
      />
    </motion.div>
  );
}

export default SelectBoxItem;
