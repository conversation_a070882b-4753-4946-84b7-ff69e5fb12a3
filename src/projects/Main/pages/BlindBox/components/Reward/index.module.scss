.container {
  width: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #FAFEDE;
  border-radius: 14px;
  padding: 10px;
  margin-top: 12px;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
}

.headerTitle {
  font-size: 14px;
  font-weight: 500;
  color: #111111;
  line-height: 20px;
}

.headerLeft {
  display: flex;
  align-items: center;
}

.headerTitle {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 20px;
}

.headerMore {
  font-size: 12px;
  font-weight: 400;
  color: #111111;
  line-height: 15px;
  cursor: pointer;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    opacity: 0.6;
  }
}

.countdownContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.countdownLabel {
  font-size: 13px;
  margin-bottom: 10px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.countdownTime {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.countdownItem {
  display: flex;
  align-items: center;
  margin: 0 2px;
}

.countdownNumber {
  background: #EAF782;
  color: #3D4600;
  font-family: PingFang SC;
  font-size: 11px;
  font-weight: 500;
  line-height: 16px;
  text-align: center;
  letter-spacing: 0px;
  padding: 3px 4px;
  border-radius: 3px;
  min-width: 17px;
}

.countdownUnit {
  font-family: PingFang SC;
  font-size: 11px;
  font-weight: 500;
  line-height: 32px;
  text-align: center;
  letter-spacing: 0px;
  color: #3D4600;
  margin-left: 2px;
}


