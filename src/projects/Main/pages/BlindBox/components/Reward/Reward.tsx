import React, { useState } from "react";
import { createPortal } from "react-dom";
import styles from "./index.module.scss";
import RewardProcess from "../RewardProcess";
import Countdown from "./Countdown";
import { Button, Toast, Dialog, Input } from "@roo/roo-b-mobile";
import PrizeRecord from "../PrizeRecord";
import PrizeDialog from "../PrizeDialog";

interface RewardProps {
  current: number;
  target: number;
  targetTime?: Date; // 倒计时目标时间
}

function Reward(props: RewardProps) {
  const { current, target, targetTime } = props;
  const [showPrizeRecord, setShowPrizeRecord] = useState(false);
  const [showPrizeDialog, setShowPrizeDialog] = useState(false);
  const [prizeDialogData, setPrizeDialogData] = useState({
    img: "",
    spuName: "",
    collectedCount: 0,
    totalCount: 0,
    remainingCount:0,
    canExchange:false,
  });

  const handlePrizeRecordClick = () => {
    setShowPrizeRecord(true);
  };

  const handlePrizeRecordClose = () => {
    setShowPrizeRecord(false);
  };

  const handleRewardProcessClick = (pointType: "third" | "middle" | "end") => {
    // 根据点击的点位类型设置不同的奖品数据
    const prizeDataMap = {
      third: {
        img: "https://p0.meituan.net/cagent/a700b39c96a71073aa2df60e1a0527eb237264.png",
        spuName: "集齐1套奖品",
        collectedCount: 1,
        totalCount: 3,
        remainingCount:998,
        canExchange:true,
      },
      middle: {
        img: "https://p0.meituan.net/cagent/6e30d0e13d0dca028ef6c58f7cbbdb6f343942.png",
        spuName: "集齐2套奖品",
        collectedCount: 2,
        totalCount: 3,
        remainingCount:998,
        canExchange:true,
      },
      end: {
        img: "https://p0.meituan.net/cagent/dd62cee3c91d6fdbe58e6555fd564cbf103497.png",
        spuName: "集齐3套奖品",
        collectedCount: 3,
        totalCount: 3,
        remainingCount:998,
        canExchange:true,
      },
    };

    setPrizeDialogData(prizeDataMap[pointType]);
    setShowPrizeDialog(true);
  };

  const handlePrizeDialogClose = () => {
    setShowPrizeDialog(false);
  };

  return (
    <div className={styles.container}>
      <div className={styles.headerContainer}>
        <div className={styles.headerLeft}>
          <span className={styles.headerTitle}>距活动结束：</span>
          {targetTime && <Countdown targetTime={targetTime} />}
        </div>
        <span className={styles.headerMore} onClick={handlePrizeRecordClick}>
          兑奖记录 &gt;
        </span>
      </div>
      <RewardProcess
        current={current}
        target={target}
        onPointClick={handleRewardProcessClick}
      />

      <PrizeRecord
        visible={showPrizeRecord}
        onCancel={handlePrizeRecordClose}
      />

      {showPrizeDialog &&
        createPortal(
          <PrizeDialog
            img={prizeDialogData.img}
            spuName={prizeDialogData.spuName}
            collectedCount={prizeDialogData.collectedCount}
            totalCount={prizeDialogData.totalCount}
            handleClose={handlePrizeDialogClose}
            remainingCount={prizeDialogData.remainingCount}
            canExchange = {prizeDialogData.canExchange}
          />,
          document.body
        )}
    </div>
  );
}

export default Reward;
