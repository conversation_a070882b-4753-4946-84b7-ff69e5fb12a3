import React, { useState, useEffect } from "react";
import styles from "./index.module.scss";

interface CountdownProps {
  targetTime: Date;
}

function Countdown(props: CountdownProps) {
  const { targetTime } = props;
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const target = targetTime.getTime();
      const difference = target - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    // 立即计算一次
    calculateTimeLeft();

    // 每秒更新一次
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [targetTime]);

  return (
    <div className={styles.countdownContainer}>
      <div className={styles.countdownTime}>
        <span className={styles.countdownItem}>
          <span className={styles.countdownNumber}>{timeLeft.days}</span>
          <span className={styles.countdownUnit}>天</span>
        </span>
        <span className={styles.countdownItem}>
          <span className={styles.countdownNumber}>{timeLeft.hours.toString().padStart(2, '0')}</span>
          <span className={styles.countdownUnit}>时</span>
        </span>
        <span className={styles.countdownItem}>
          <span className={styles.countdownNumber}>{timeLeft.minutes.toString().padStart(2, '0')}</span>
          <span className={styles.countdownUnit}>分</span>
        </span>
        <span className={styles.countdownItem}>
          <span className={styles.countdownNumber}>{timeLeft.seconds.toString().padStart(2, '0')}</span>
          <span className={styles.countdownUnit}>秒</span>
        </span>
      </div>
    </div>
  );
}

export default Countdown; 