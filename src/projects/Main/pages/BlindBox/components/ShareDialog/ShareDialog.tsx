import React, { useEffect, useState, useRef } from "react";
import styles from "./index.module.scss";
import { Dialog, Icon, Image } from "@roo/roo-b-mobile";
import {
  appShareChannel,
  appShareImage,
  appDownloadImage,
} from "@/projects/Main/utils/shareUtil";
import { reportShareImageMv, reportShareImageMc } from "../../constants/report";

interface ShareDialogProps {
  open: boolean;
  shareImg: string | undefined;
  shareOriginImg: string | undefined;
  spuName: string;
  content: string;
  cardId: number; // 卡牌ID，用于埋点
  handleClose: () => void;
}

function ShareDialog(props: ShareDialogProps) {
  const {
    open,
    spuName,
    content,
    shareImg,
    shareOriginImg,
    cardId,
    handleClose,
  } = props;

  // 弹窗曝光埋点
  useEffect(() => {
    if (open) {
      reportShareImageMv();
    }
  }, [open]);

  if (!open) {
    return null;
  }
  // 分享到微信
  const handleShareWinxinClick = () => {
    reportShareImageMc(cardId, 1);
    appShareImage(
      shareImg ?? "",
      shareOriginImg ?? "",
      window.KNB.shareImage.WECHAT_FRIENDS
    );
  };

  /// 分享到朋友圈
  const handleShareTimelineClick = () => {
    reportShareImageMc(cardId, 2);
    appShareImage(
      shareImg ?? "",
      shareOriginImg ?? "",
      window.KNB.shareImage.WECHAT_TIMELINE
    );
  };

  /// 保存图片到本地
  const handleSaveImageClick = () => {
    reportShareImageMc(cardId, 3);
    if (shareOriginImg) {
      appDownloadImage(shareOriginImg);
    }
  };

  const shareBtns = [
    {
      key: "wechat",
      text: "微信好友",
      img: "https://p0.meituan.net/cagent/24bf2f7361fc3eadbf807f22661a0fd34064.png",
      onClick: handleShareWinxinClick,
    },
    {
      key: "timeline",
      text: "朋友圈",
      img: "https://p0.meituan.net/cagent/cd1476ed673ba97f7a6f54e3790e8e914190.png",
      onClick: handleShareTimelineClick,
    },
    {
      key: "save",
      text: "保存图片",
      img: "https://p0.meituan.net/cagent/0e21d83ab700419f1ffd66d8660715234010.png",
      onClick: handleSaveImageClick,
    },
  ];

  return (
    <div className={styles.box} onClick={handleClose}>
      <img
        className={styles.img}
        src={shareImg}
        alt="装饰"
        onClick={(e) => e.stopPropagation()}
        //loading="lazy"
      />
      <div className={styles.boxBtnGroup} onClick={(e) => e.stopPropagation()}>
        {shareBtns.map((btn) => (
          <div className={styles.shareBtn} onClick={btn.onClick} key={btn.key}>
            <img src={btn.img} className={styles.shareBtnImg} alt={btn.text} />
            <span className={styles.shareBtnText}>{btn.text}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ShareDialog;
