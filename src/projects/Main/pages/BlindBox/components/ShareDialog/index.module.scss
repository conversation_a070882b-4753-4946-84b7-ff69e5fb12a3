.box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 40px 0 40px 0;
  z-index: 999;
  overflow-y: auto;
}

.img {
  width: 305px;
  height: 464px;
  margin: auto;
}

.boxBtnGroup {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  width: 80%;
  margin-top: 12px;
  padding: 0 24px;
}

.shareBtn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-img {
    width: 50px;
    height: 50px;
    margin-bottom: 8px;
  }
  &-text {
    color: #fff;
    font-size: 14px;
    text-align: center;
  }
}
