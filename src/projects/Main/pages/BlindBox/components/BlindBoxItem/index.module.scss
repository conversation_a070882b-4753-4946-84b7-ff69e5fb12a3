.container {
  position: relative;
  flex: 1;
  width: 100%;
  overflow: hidden;
  border: 1px solid #FFF;
  border-radius: 12px;
  aspect-ratio: 0.76;
}

.background {
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-color: #F9FFCB;
  opacity: 0.3;
}

// 待领取标签样式
.pending-tag {
  position: absolute;
  top: -12%;
  left: -16%;
  z-index: 2;
  width: 55%;
  height: 42%;
  pointer-events: none;
  
  &-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 隐藏款标签样式
.hidden-tag {
  position: absolute;
  top: 1px;
  right: 1px;
  z-index: 2;
  width: 34%;
  height: 26%;
  padding: 0;
  background: transparent;
  border-radius: 0;
  pointer-events: none;
  
  &-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 卡牌图片样式
.card {
  position: absolute;
  top: 5%;
  left: 50%;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 95%;
  height: auto;
  transform: translateX(-50%);
  pointer-events: none;
  
  &-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }

  // 约束placeholder尺寸
  :global(.rbm-image-placeholder) {
    img {
      max-width: 60px; // 或者40%
      max-height: 60px; // 或者40%
    }
  }
}

// 底部胶囊样式
.bottom {
  position: absolute;
  bottom: 9.5%;
  left: 50%;
  z-index: 2;
  width: 73%;
  height: 17%;
  padding: 1.9px;
  background: linear-gradient(179deg, #FBFFDE 32%, #E1FF00 102%);
  border-radius: 9999px;
  transform: translateX(-50%);
  pointer-events: none;

  &-label {
    position: relative;
    display: flex;
    align-items: baseline;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #5F6720;
    font-size: 14px;
    line-height: 1;
    text-align: center;
    background: linear-gradient(93deg, #E7FA55 3%, #F4FFA3 85%);
    border-radius: 9999px;
  }
  
  &-card-name {
    display: inline-flex;
    align-items: center;
    height: 100%;
    padding-right: 2.5%;
    font-weight: 400;
    font-family: FZLanTYJ, sans-serif;
  }
  
  &-multiply-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: auto;
    height: 25%;
    padding-right: 1.5%;
    line-height: 0;
    
    &-image {
      display: block;
      width: auto;
      height: 100%;
      object-fit: contain;
    }
  }
  
  &-card-quantity {
    display: inline-flex;
    align-items: center;
    height: 100%;
    font-weight: 500;
    font-family: "Meituan Type-Light", sans-serif;
  }
}

// 未收集的卡片样式
.container.uncollected {
  .background {
    background-color: #EAEDF6;
    opacity: 0.3;
  }
  
  .hidden-tag {
    mix-blend-mode: luminosity;
  }
  
  .bottom {
    background: linear-gradient(104deg, #EAEBEE 9%, #FFF 52%, #F0F0F0 92%);
  }
  
  .bottom-label {
    color: #707378;
    background: linear-gradient(179deg, #F3F3F3 32%, #E8E8E8 102%);
  }
}