import React from "react";
import styles from "./index.module.scss";
import { Image } from "@roo/roo-b-mobile";
import { CardType, CollectionStatus } from "../../constants/BlindBoxConstants";
import { ICollectionCard } from "../../../../services/blindbox/fetchBlindBox";
import { reportBlindBoxItemMc } from "../../constants/report";
import { is } from "@/.common";

// 盲盒Item组件类型定义
export interface BlindBoxItemProps {
  item: ICollectionCard;
  index: number;
  onClick: (index: number) => void;
}

const BlindBoxItem: React.FC<BlindBoxItemProps> = ({
  item,
  index,
  onClick,
}) => {
  // 判断是否为隐藏款
  const isHidden = item.cardType === CardType.MYSTERY;

  // 判断是否待领取
  const isPending = item.collectionStatus === CollectionStatus.DRAWN;

  // 判断是否已获得该卡片,转赠中也是已获得
  const isCollected = item.collectionStatus === CollectionStatus.OWNED;

  const isGive = item.collectionStatus === CollectionStatus.PENDING_CLAIM;
  // 根据状态决定使用的卡牌图片
  const cardImageUrl =
    isCollected || isPending || isGive ? item.imageUrl : item.unOwnedImageUrl;

  // 根据状态决定展示的卡牌名称
  const cardName =
    isPending || isCollected || isGive || !isHidden
      ? item.cardName || "奶茶"
      : "？？？？";

  // 根据收集状态选择不同的隐藏款图标
  const hiddenTagImageUrl =
    isCollected || isGive
      ? "https://p0.meituan.net/cagent/155fd14a2fa1cde6466901d3cd7712cf14962.png" // 已收集的隐藏款图标
      : "https://p0.meituan.net/cagent/5a07412b9fdfa8acfc12c065f4a8526c68069.png"; // 未收集的隐藏款图标

  // 处理点击事件，添加埋点
  const handleClick = () => {
    // 根据收集状态确定 status 值
    let status = 0;
    if (isCollected) {
      status = 3; // 已获得
    } else if (isPending) {
      status = 2; // 待领取
    } else if (isGive) {
      status = 4;
    } else {
      status = 1; // 未获得
    }

    // 触发埋点
    reportBlindBoxItemMc(item.cardId, status);

    // 调用原有的点击回调
    onClick(index);
  };

  return (
    <div
      className={`${styles.container} ${!isCollected && !isGive ? styles.uncollected : ""}`}
      onClick={handleClick}
    >
      {/* 背景层 */}
      <div className={styles.background}></div>

      {/* 左上角待领取提示条 */}
      {isPending && (
        <div className={styles.pendingTag}>
          <Image
            src="https://p0.meituan.net/cagent/9283d2bb157df7559d54572548b64a8a8582.png"
            alt="待领取"
            className={styles.pendingTagImage}
            //loading="lazy"
          />
        </div>
      )}

      {/* 右上角隐藏款图片标签 */}
      {isHidden && (
        <div className={styles.hiddenTag}>
          <Image
            src={hiddenTagImageUrl}
            alt="隐藏款"
            className={styles.hiddenTagImage}
            //loading="lazy"
          />
        </div>
      )}

      {/* 卡牌图片 */}
      <div className={styles.card}>
        <Image
          src={cardImageUrl}
          alt={cardName}
          className={styles.cardImage}
          role="button"
          tabIndex={0}
          aria-label={cardName}
          //loading="lazy"
        />
      </div>

      {/* 底部胶囊标签 */}
      <div className={styles.bottom}>
        <div className={styles.bottomLabel}>
          {/* 卡牌名称部分 */}
          <span className={styles.bottomCardName}>{cardName}</span>

          {/* 只有当数量大于1时才显示乘号和数量 */}
          {item.quantity > 1 && (
            <>
              {/* 乘号图片 */}
              <span className={styles.bottomMultiplyIcon}>
                <Image
                  src="https://p0.meituan.net/cagent/526698d1cdbf9fb0465241b811f3e1dc355.png"
                  alt="×"
                  className={styles.bottomMultiplyIconImage}
                  //loading="lazy"
                />
              </span>

              {/* 数量部分 */}
              <span className={styles.bottomCardQuantity}>{item.quantity}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlindBoxItem;
