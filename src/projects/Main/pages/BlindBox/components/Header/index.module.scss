.header {
  position: relative;
  height: auto;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  justify-items: center;
  //padding-top: 44px;
  padding-top: 20px;
  &-rule {
    position: absolute;
    top: 90px;
    right: 0px;
    z-index: 990;
    cursor: pointer;
    
    &-img {
      width: 20px;
      height: 48px;
      object-fit: contain;
    }
  }

  &-back {
    position: absolute;
    top: 52px;
    left: 12px;
    z-index: 1;
    cursor: pointer;
    &-img {
      width: 10px;
      height: 18px;
      object-fit: contain;
    }
  }

  &-share {
    position: absolute;
    top: 52px;
    right: 12px;
    z-index: 1;
    cursor: pointer;
    
    &-img {
      width: 21px;
      height: 18px;
      object-fit: contain;
    }
  }

  &-title {
    height: auto;
    width: 100%;
    padding-left: 32px;
    padding-right: 32px;
    margin-top: 20px;
    z-index: 1;
    position: relative;
    &-img {
      width: 100%;
      height: auto;
      object-fit: contain;
      display: block;
      max-width: 100%;
      vertical-align: top;
      /* 防止布局偏移 */
      contain: layout style paint;
      will-change: auto;
      /* 确保图片加载时不会造成布局偏移 */
      min-height: 0;
      min-width: 0;
    }
  }
  &-content{
    z-index: 1;
    position: relative;
    &-img{
      width: 186px;
      height: auto;
      display: block;
    }
  }
}

.introduce {
  &-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 21px;
    margin-top: 12px;
  }

  &-text {
    font-size: 12px;
    font-weight: normal;
    letter-spacing: 0px;
    color: #ffffff;
  }
}
