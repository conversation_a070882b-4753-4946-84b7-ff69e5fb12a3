import React, { useEffect, useState } from "react";
import styles from "./index.module.scss";
import { Dialog, SafeArea, Toast } from "@roo/roo-b-mobile";
import universalGoback from "@common/baseUtils/universalGoback";
import { appShareH5, isWeixin } from "@/projects/Main/utils/shareUtil";
import { buildSchemeUrl } from "@/constants/scheme";
import { reportShareH5BtnMc, reportRuleBtnMc } from "../../constants/report";
import { ACTIVITY_RULE } from "../../constants/rule";
import { env } from "@wmfe/intelligent_common";

const fxUrl = "https://i.meituan.com/c/YzhlNTE5ZjYt";

function Header() {
  const [visible, setVisible] = useState(false);

  const onBack = () => {
    universalGoback();
  };

  useEffect(() => {
    const isTitans = window.KNB?.env?.isTitans;
    const isWM = window.KNB?.env?.isWM;
    const isWX = isWeixin();
    console.log("ua iswx", isWX);
    console.log("env.isWX:", env.isWX);
    console.log("knb.env.iswx", window.KNB.env.isWX);
    console.log("env.isMT", env.isMT);
    console.log("env.isWM", env.isWM);
    console.log("env.isBrowser", env.isBrowser);
    console.log("window.KNB?.env.isBrowser", window.KNB?.env.isBrowser);
    console.log("env.isTitans", window.KNB?.env?.isTitans);
    if (isWX && !isTitans) {
      try {
        window.location.href = fxUrl;
      } catch (error) {
        console.warn(error);
        Toast.open({
          content: "跳转失败，请稍后再试",
        });
      }
    }
    
  }, []);


  const handleRuleClick = () => {
    // 上报规则按钮点击埋点
    reportRuleBtnMc();
    setVisible(true);
  };

  const handleShareClick = () => {
    // 上报分享按钮点击埋点
    reportShareH5BtnMc();
    const url = window.location.href;
    console.log(url);

    // let url;
    // if (env.isIOS) {
    //   const originalUrl = buildSchemeUrl(window.location.href);
    //   url = `https://i.meituan.com/web?url=${encodeURIComponent(originalUrl)}`;
    // } else {
    //   url = "https://i.meituan.com/c/YjBlZWZmMmMt";
    // }
    appShareH5({
      title: "不知道喝啥，抽个盲盒试试！",
      desc: "看看你今天的幸运奶茶是什么？",
      image:
        "https://p0.meituan.net/cagent/847e1080198c3a2d1334b29487372ef218374.png",
      url: url,
    });
  };

  return (
    <div className={styles.header}>
       <SafeArea position="top"/>
      <div className={styles.headerRule} onClick={handleRuleClick}>
        <img
          src="https://p0.meituan.net/cagent/237359ece46739d7965794e88010e98a2359.png"
          alt="right top"
          className={styles.headerRuleImg}
          //loading="lazy"
        />
      </div>
      <div className={styles.headerBack} onClick={onBack}>
        <img
          src="https://p0.meituan.net/cagent/2471fab9d3867b1727a53dfd63931755244.png"
          alt="share"
          className={styles.headerShareImg}
          //loading="lazy"
        />
      </div>
      <div className={styles.headerShare} onClick={handleShareClick}>
        <img
          src="https://p0.meituan.net/cagent/d9eecacab7514a19f6b28e2c548b5827648.png"
          alt="share"
          className={styles.headerShareImg}
          //loading="lazy"
        />
      </div>
      <div className={styles.headerTitle}>
        <img
          src="https://p0.meituan.net/cagent/39fe457d884dc57196fcf7457329237c19444.webp"
          alt="line"
          className={styles.headerTitleImg}
          loading="eager"
          decoding="async"
        />
      </div>
      <div className={styles.introduceRow}>
        <span className={styles.introduceText}>❶拆开灵感盲盒</span>
        <span className={styles.introduceText}>❷下单同款奶茶</span>
        <span className={styles.introduceText}>❸获得数字盲盒</span>
      </div>
      <Dialog
        open={visible}
        onCancel={() => setVisible(false)}
        cancelText={null}
        onOk={() => setVisible(false)}
        title="活动规则"
        position="bottom"
        content={
          <div
            style={{
              whiteSpace: "pre-line",
              lineHeight: "1.6",
              fontSize: "14px",
              color: "#333",
              textAlign: "left",
            }}
          >
            {ACTIVITY_RULE}
          </div>
        }
      />
    </div>
  );
}

export default Header;
