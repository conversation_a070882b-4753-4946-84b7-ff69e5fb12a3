import React, { useState } from "react";
import styles from "./index.module.scss";
import { Dialog } from "@roo/roo-b-mobile";
import AddressEdit from "../AddressEdit";

interface PrizeRecordProps {
  visible?: boolean;
  onCancel?: () => void;
}

interface PrizeRecordItem {
  id: string;
  image: string;
  spuName: string;
  content: string;
  logisticsStatus: "未发货" | "已寄出" | "已送达";
  buttonText?: string;
  buttonAction?: () => void;
  isFreeShipping?: boolean;
}

const PrizeRecord: React.FC<PrizeRecordProps> = ({
  visible = false,
  onCancel,
}) => {
  const [addressEditVisible, setAddressEditVisible] = useState(false);
  const [selectedRecordId, setSelectedRecordId] = useState<string>("");

  const handleCancel = () => {
    onCancel?.();
  };

  const handleFillAddress = (id: string) => {
    setSelectedRecordId(id);
    setAddressEditVisible(true);
  };

  const handleViewLogistics = (id: string) => {
    console.log("查看物流", id);
  };

  const handleAddressEditCancel = () => {
    setAddressEditVisible(false);
    setSelectedRecordId("");
  };

  const handleAddressEditOk = (addressData: any) => {
    console.log("地址保存成功", addressData);
    setAddressEditVisible(false);
    setSelectedRecordId("");
    // 这里可以调用API保存地址信息
  };

  // 静态数据，最多3个奖品
  const records: PrizeRecordItem[] = [
    {
      id: "1",
      image:
        "https://p0.meituan.net/cagent/7bd37497fad29083a9a36c11a7f1f3c552184.webp",
      spuName: "非人斋·色卡一套(5张)",
      content: "填写后3个工作日内寄出",
      logisticsStatus: "未发货",
      buttonText: "填写地址",
      buttonAction: () => handleFillAddress("1"),
      isFreeShipping: true,
    },
    {
      id: "2",
      image:
        "https://p0.meituan.net/cagent/7bd37497fad29083a9a36c11a7f1f3c552184.webp",
      spuName: "非人斋·冰箱贴一套(5张)",
      content: "填写后3个工作日内寄出",
      logisticsStatus: "已寄出",
      buttonText: "查看物流",
      buttonAction: () => handleViewLogistics("2"),
    },
    {
      id: "3",
      image:
        "https://p0.meituan.net/cagent/7bd37497fad29083a9a36c11a7f1f3c552184.webp",
      spuName: "非人斋・金贴(1张)",
      content: "填写后3个工作日内寄出",
      logisticsStatus: "已寄出",
    },
  ];

  const dialogContent = (
    <div className={styles.prizeRecord}>
      {/* 自定义header，样式与BlindBox的gridTitle一致 */}
      <div className={styles.customHeader}>
        <img
          className={styles.headerLine}
          src="https://p0.meituan.net/cagent/25be9c3c496e534be88623b87d946598230.png"
          alt=""
          loading="lazy"
          decoding="async"
          width="34"
          height="1"
        />
        <img
          className={styles.headerXing}
          src="https://p0.meituan.net/cagent/0e5c49b4ec3ddc2b6f91edf09c950d13389.png"
          alt=""
          loading="lazy"
          decoding="async"
          width="12"
          height="12"
        />
        <div className={styles.headerText}>我的兑奖记录</div>
        <img
          className={styles.headerXing}
          src="https://p0.meituan.net/cagent/0e5c49b4ec3ddc2b6f91edf09c950d13389.png"
          alt=""
          loading="lazy"
          decoding="async"
          width="12"
          height="12"
        />
        <img
          className={styles.headerLine}
          src="https://p0.meituan.net/cagent/5fd751479db6dd43cb3b64a48753d3bd232.png"
          alt=""
          loading="lazy"
          decoding="async"
          width="34"
          height="1"
        />
      </div>

      {/* 记录列表 */}
      <div className={styles.recordList}>
        {records.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📋</div>
            <div className={styles.emptyText}>暂无兑奖记录</div>
            <div className={styles.emptySubText}>集齐盲盒后即可兑奖</div>
          </div>
        ) : (
          records.map((record) => (
            <div key={record.id} className={styles.recordItem}>
              {/* 左侧图片 */}
              <div className={styles.itemImage}>
                <img
                  src={record.image}
                  alt={record.spuName}
                  loading="lazy"
                  decoding="async"
                  width="60"
                  height="60"
                />
              </div>

              {/* 中间内容区域 */}
              <div className={styles.itemContent}>
                <div className={styles.itemTop}>
                  <div className={styles.itemTitle}>{record.spuName}</div>
                  <div className={styles.itemDescription}>{record.content}</div>
                </div>
                <div className={styles.itemBottom}>
                  <div className={styles.logisticsStatus}>
                    物流状态：{record.logisticsStatus}
                  </div>
                  {/* 右侧按钮区域 */}
                  <div className={styles.itemActions}>
                    {record.buttonText && (
                      <button
                        className={styles.actionButton}
                        onClick={record.buttonAction}
                      >
                        {record.buttonText}
                        {record.isFreeShipping && (
                          <div className={styles.freeShippingTag}>包邮</div>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );

  return (
    <>
      <Dialog
        open={visible}
        onCancel={handleCancel}
        cancelText={null}
        okText={null}
        title=""
        content={dialogContent}
        destroyOnClose
        position="bottom"
        className={styles.customDialog}
      />
      <AddressEdit
        visible={addressEditVisible}
        onCancel={handleAddressEditCancel}
        onOk={handleAddressEditOk}
      />
    </>
  );
};

export default PrizeRecord;
