.prizeRecord {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
  padding-bottom: 24px;
}

// 只针对当前组件的Dialog样式
.customDialog {
  :global(.rbm-dialog-wrapper) {
    padding: 0 !important;
  }

  :global(.rbm-dialog-inner-content) {
    margin: 0 !important;
    padding: 0 !important;
  }
}

// 自定义header，样式与BlindBox的gridTitle一致
.customHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 18px;
  background: linear-gradient(180deg, #F4FFA3 0%, #ffffff 100%);
  padding: 16px 0;
  border-radius: 12px 12px 0 0;
  margin: 0;
  box-sizing: border-box;
}

.headerText {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  color: #3d4600;
  text-align: center;
  font-family: "<PERSON><PERSON>";
  letter-spacing: 0.06em;
  margin-left: 12px;
  margin-right: 12px;
}

.headerLine {
  width: 34px;
  height: 1px;
  margin: 0 6px;
}

.headerXing {
  height: 12px;
  width: 12px;
}

// 记录列表容器
.recordList {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.emptyText {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  color: #666666;
  margin-bottom: 8px;
}

.emptySubText {
  font-family: PingFang SC;
  font-size: 14px;
  color: #999999;
}

// 记录项样式 - 新的布局结构
.recordItem {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #e0e0e0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 左侧图片
.itemImage {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 中间内容区域
.itemContent {
  flex: 1;
  min-width: 0; // 防止文本溢出
  height: 80px; // 与图片高度一致
  display: flex;
  flex-direction: column;
  justify-content: space-between; // 垂直两端对齐
  text-align: left;
}

.itemTop {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.itemBottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.itemTitle {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  text-align: left;
}

.itemDescription {
  font-family: PingFang SC;
  font-size: 14px;
  color: #666666;
  line-height: 1.4;
  text-align: left;
}

.logisticsStatus {
  font-family: PingFang SC;
  font-size: 12px;
  color: #999999;
  line-height: 1.4;
  text-align: left;
}

// 右侧按钮区域
.itemActions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  position: relative;
}

.freeShippingTag {
  position: absolute;
  top: -10px;
  right: -6px;
  background: #ffb3d9;
  color: #ffffff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  line-height: 1;
  z-index: 1;
}

.actionButton {
  background: #ff4d4f;
  color: #ffffff;
  border: none;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  font-family: PingFang SC;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1;
  position: relative;
  
  &:hover {
    background: #ff7875;
  }
  
  &:active {
    background: #d9363e;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .recordList {
    padding: 0 16px;
  }
  
  .recordItem {
    padding: 12px;
    gap: 10px;
  }
  
  .itemImage {
    width: 50px;
    height: 50px;
  }
  
  .itemContent {
    height: 50px; // 与响应式图片高度一致
  }
  
  .itemTitle {
    font-size: 14px;
  }
  
  .itemDescription {
    font-size: 13px;
  }
  
  .actionButton {
    padding: 5px 10px;
    font-size: 11px;
  }
} 