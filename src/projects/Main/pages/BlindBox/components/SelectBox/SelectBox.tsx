import React from "react";
import styles from "./index.module.scss";
import { IDrawnCard } from "@/projects/Main/services/blindbox/fetchBlindBox";
import SelectBoxItem from "../SelectBoxItem";
import { getLottieAssetUrl } from "@/projects/Main/utils/assetUtil";
import { usePreloadLottieData } from "@/hooks/use-lottie-animation";

interface SelectBoxProps {
  handleBoxClick: (index: number) => void;
  handleRandomBoxClick: () => void;
  cardList?: IDrawnCard[];
  count?: number;
  closingAnimationSlot?: number | null;
  onAnimationComplete?: () => void;
}

const DEFAULT_BOX_IMAGE =
  "https://p0.meituan.net/cagent/712a3e2e5d41fa17b6f6d0123e25619624164.png";

function SelectBox(props: SelectBoxProps) {
  const {
    handleBoxClick,
    handleRandomBoxClick,
    cardList = [],
    count = 0,
    closingAnimationSlot = null,
    onAnimationComplete,
  } = props;

  // 使用预加载hook
  const lottieDataUrl = getLottieAssetUrl('SELECT_BOX_ITEM');
  const { loadedData, isLoading, error } = usePreloadLottieData([lottieDataUrl]);

  const getCardBySlot = (slot: number) => {
    return cardList.find((card) => card.drawSlot === slot);
  };

  return (
    <div className={styles.gui}>
      <img
        src="https://p0.meituan.net/cagent/2084d6460ac1b84f5672a39c3c4768c845240.webp"
        alt="header"
        className={styles.guiImg}
        //loading="lazy"
      />
      <div className={styles.box}>
        <div className={styles.boxRow}>
          {[1, 2, 3].map((slot) => {
            const card = getCardBySlot(slot);
            return (
              <div key={slot} className={styles.boxRowItem}>
                <SelectBoxItem
                  handleBoxClick={() => handleBoxClick?.(slot)}
                  cardName={card?.cardName || `待拆开`}
                  imageUrl={card?.imageUrl || DEFAULT_BOX_IMAGE}
                  drawStatus={card?.drawStatus || 0}
                  isOpen={!!card}
                  isClosingAnimation={slot === closingAnimationSlot}
                  onAnimationComplete={onAnimationComplete}
                  preloadedLottieData={loadedData.get(lottieDataUrl)} // 传递预加载的数据
                  isLottieLoaded={!isLoading && !error} // 传递加载状态
                ></SelectBoxItem>
              </div>
            );
          })}
        </div>
      </div>
      <div className={styles.btn} onClick={handleRandomBoxClick}>
        <span className={styles.btnText}>
          随机拆盲盒{count > 0 ? `x${count}` : ""}
        </span>
      </div>
    </div>
  );
}

export default SelectBox;
