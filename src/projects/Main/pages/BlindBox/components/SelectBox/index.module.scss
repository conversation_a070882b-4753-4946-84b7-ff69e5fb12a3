.gui {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.guiImg {
  width: 100%;
  height: auto;
  display: block;
}

.box {
  position: absolute;
  top: 10%;
  width: 80%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  z-index: 1;

  &-row {
    display: flex;
    width: 100%;
    gap: 10px;

    &-item {
      position: relative;
      display: flex;
      width: calc((100% - 20px)/3);
      justify-content: center;
      align-items: center;
    }
  }
}


.btn {
  position: absolute;
  left: 50%;
  top: 50.43%;
  transform: translate(-50%, -50%);
  height: 50px;
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 22px;
  border-radius: 5000px;
  font-weight: 600;
  text-align: center;
  font-size: 18px;
  color: #FFFFFF;
  cursor: pointer;
  // background: linear-gradient(286deg, #ff639f 4%, #ffb3d1 126%);
  &-text {
    font-size: 18px;
    font-weight: 400;
    line-height: 18px;
    color: #FFFFFF;
    font-family: "MF YuanHei";
    letter-spacing: 1.12px;
  }
}