import { useEffect, useState } from "react";
import React from "react";
import { isNotEmptyString } from "@/projects/Main/utils/stringUtil";
import { Image, Loading } from "@roo/roo-b-mobile";
import { AnimatePresence, motion } from "framer-motion";
import styles from "./index.module.scss";
// import collectionAnimationData from "@/assets/lottie/collection-popup-lottie.json";
import {
  useLottieAnimation,
  usePreloadLottieData,
} from "@/hooks/use-lottie-animation";
import { getLottieAssetUrl } from "@/projects/Main/utils/assetUtil";
import AddressEdit from "@/projects/Main/pages/BlindBox/components/AddressEdit";

interface PrizeDialogProps {
  img: string | undefined; // 展示兜底图
  spuName: string;
  collectedCount: number;
  totalCount: number;
  remainingCount: number; // 剩余份数
  canExchange?: boolean; // 是否可兑换
  handleClose: () => void;
  onExchange?: () => void; // 兑换回调函数
}

function PrizeDialog(props: PrizeDialogProps) {
  const {
    spuName,
    collectedCount,
    totalCount,
    remainingCount,
    img,
    canExchange = false,
    handleClose,
    onExchange,
  } = props;
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [shouldPlayAnimation, setShouldPlayAnimation] =
    useState<boolean>(false);
  const [contentAnimationKey, setContentAnimationKey] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [showAddressEdit, setShowAddressEdit] = useState<boolean>(false);

  // 预加载lottie动画数据
  const collectionAnimationUrl = getLottieAssetUrl("COLLECTION_POPUP");
  const { loadedData, isLoading, error } = usePreloadLottieData([
    collectionAnimationUrl,
  ]);

  // 使用通用动画hook
  const collectionAnimation = useLottieAnimation();

  /**
   * 初始化并播放 lottie 动画
   * 在组件挂载时加载动画，在卸载时销毁，避免内存泄漏
   */
  useEffect(() => {
    if (!collectionAnimation.animationRef.current || isLoading || error) return;

    // 使用预加载的数据
    const collectionAnimationData = loadedData.get(collectionAnimationUrl);
    if (collectionAnimationData) {
      console.log("[DEBUG] 使用预加载的收藏动画数据");
      collectionAnimation.playAnimation(collectionAnimationData);
    } else {
      console.warn("[WARN] 收藏动画数据未预加载，使用备用加载方式");
      // 备用加载方式
      collectionAnimation
        .loadLottieData(collectionAnimationUrl)
        .then((data) => {
          collectionAnimation.playAnimation(data);
        });
    }
  }, [
    isLoading,
    error,
    loadedData,
    collectionAnimationUrl,
    collectionAnimation,
  ]);

  /**
   * 监听shouldPlayAnimation状态变化，播放动画
   */
  useEffect(() => {
    if (shouldPlayAnimation && collectionAnimation.animationRef.current) {
      // 使用通用hook的播放方法
      const collectionAnimationData = loadedData.get(collectionAnimationUrl);
      if (collectionAnimationData) {
        collectionAnimation.playAnimation(collectionAnimationData);
      }
      setShouldPlayAnimation(false);
    }
  }, [
    shouldPlayAnimation,
    loadedData,
    collectionAnimationUrl,
    collectionAnimation,
  ]);

  const handleCloseClick = () => {
    setIsOpen(false);
  };

  const handleExchangeClick = () => {
    // 弹出地址编辑弹窗
    setShowAddressEdit(true);
  };

  const handleAddressEditOk = (addressData: any) => {
    console.log("地址信息:", addressData);
    setShowAddressEdit(false);

    // 如果有兑换回调，执行兑换逻辑
    if (onExchange) {
      onExchange();
    }
  };

  const handleAddressEditCancel = () => {
    setShowAddressEdit(false);
  };

  // 蒙层动画配置
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 0.85,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  // 弹窗内容动画配置 - 弹簧效果
  const contentVariants = {
    hidden: { scale: 0.4, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        damping: 5, // 摩擦力
        stiffness: 20, // 张力
        mass: 0.5,
      },
    },
    exit: {
      scale: 0.8,
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  return (
    <>
      <AnimatePresence
        onExitComplete={() => {
          handleClose();
        }}
      >
        {isOpen && (
          <>
            <motion.div
              className={styles.overlay}
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={overlayVariants}
            />
            {/* lottie 动画容器：位于 overlay 与弹窗之间 */}
            <div
              ref={collectionAnimation.animationRef}
              className={styles["lottie-container"]}
            />
            <motion.div
              className={styles.box}
              key={contentAnimationKey}
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={contentVariants}
            >
              <div>
                <div className={styles.boxTitle}>{spuName}</div>
                <div className={styles.boxSubtitle}>
                  已收集
                  <span className={styles.boxSubtitleNumber}>
                    {collectedCount}/{totalCount}
                  </span>
                  款角色，再集{totalCount - collectedCount}款可兑奖
                </div>
              </div>

              <div className={styles.boxCard}>
                {isNotEmptyString(img) ? (
                  <img className={styles.boxCardImage} src={img} alt="card" />
                ) : null}
                {!canExchange && (
                  <div className={styles.boxCardRemainingContainer}>
                    <div className={styles.boxCardRemaining}>
                      剩余{remainingCount}份
                    </div>
                  </div>
                )}
              </div>
              {canExchange && (
                <div className={styles.boxBtnGroup}>
                  {/* 主要操作按钮：根据模式显示不同的文案和回调 */}
                  <div className={styles.boxBtnContent}>
                    兑换后会消耗【收集进度】里的一套角色
                  </div>
                  {/* <button
                    className={styles.boxBtn}
                    onClick={handleExchangeClick}
                    type="button"
                    disabled={loading}
                  >
                    立即兑换
                  </button> */}
                  <button className={styles.boxImageBtn}>
                    <img
                      src="https://p0.meituan.net/cagent/70a04a90779d08a5b0bac3a3c3f8e17a23679.png"
                      alt="按钮图片"
                      className={styles.boxImageBtnImage}
                    />
                  </button>
                </div>
              )}
              <div
                className={styles.boxClose}
                aria-label="关闭"
                onClick={handleCloseClick}
              >
                <Image
                  className={styles.boxCloseImage}
                  src="https://p0.meituan.net/cagent/a629fb6be7984c646ac129207362a0111361.png"
                  alt="关闭"
                />
              </div>
            </motion.div>

            {/* Loading 遮罩层 */}
            {loading && (
              <div className={styles.loadingOverlay}>
                <Loading spinning iconType="vline" />
              </div>
            )}
          </>
        )}
      </AnimatePresence>

      {/* 地址编辑弹窗 */}
      <AddressEdit
        visible={showAddressEdit}
        onOk={handleAddressEditOk}
        onCancel={handleAddressEditCancel}
      />
    </>
  );
}

export default PrizeDialog;
