@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: fixed;
  z-index: 997;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0);
}

/* lottie 动画容器：居中定位，层级位于 overlay 与 box 之间 */
.lottie-container {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 998;
  width: 100vw;
  height: auto;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.box {
  position: fixed;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  overflow: hidden;

  &-title {
    width: 100%;
    font-weight: 400;
    font-size: 33px;
    font-family: "MF YuanHei", sans-serif;
    font-style: italic;
    line-height: 33px;
    letter-spacing: 2px;
    text-align: center;
    background: linear-gradient(175deg, #FFFDF2 28%, #FFF5B2 77%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    flex-shrink: 0;
  }

  &-subtitle {
    width: 100%;
    color: #fff;
    font-weight: 400;
    font-size: 22px;
    font-family: "MF YuanHei", sans-serif;
    line-height: 33px;
    letter-spacing: 2px;
    text-align: center;
    flex-shrink: 0;

    &-number {
      margin: 0 4px;
      font-weight: 700;
      font-size: 22px;
      font-family: "Meituan Type-Regular", sans-serif;
      font-variation-settings: "opsz" auto;
    }
  }

  &-card {
    @include flex-center;

    width: auto;
    max-width: 70%; /* 进一步缩小卡片宽度 */
    flex: 0 1 auto; /* 改为不伸缩，让卡片保持自然大小 */
    min-height: 0;
    position: relative;
    margin-bottom: 42px; /* 增加卡片与按钮间距，为cardRemaining留出空间 */
    margin-top: 30px;

    &-image {
      width: auto;
      max-width: 100%;
      height: auto;
      max-height: 100%;
      object-fit: contain;
      object-position: center;
    }

    &-remaining-container {
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      width: 170px;
      height: 48px;
      border-radius: 691px;
      opacity: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(104deg, #EAEBEE 9%, #FFFFFF 52%, #F0F0F0 92%);
      border: 1.9px solid;
      border-image: linear-gradient(179deg, #F3F3F3 32%, #E8E8E8 102%) 1.9;
    }

    &-remaining {
      color: #111111;
      font-weight: 500;
      font-size: 14px;
      line-height: 14px;
      text-align: center;
      white-space: nowrap;
    }
  }

  /* 按钮组：横向排列分享和继续按钮 */
  &-btn-group {
    @include flex-center;
    flex-direction: column;
    gap: 10px;
    flex-shrink: 0;
  }

  &-btnContent {
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    line-height: 1.4;
    max-width: 280px;
  }

  &-btn {
    width: 170px;
    height: 50px; /* 明确设置高度为50px，与按钮组高度一致 */
    color: #FFFFFF;
    font-weight: normal;
    font-size: 20px;
    font-family: "MF YuanHei", sans-serif;
    line-height: 20px;
    letter-spacing: 0.03em;
    text-align: center;
    background: linear-gradient(317deg, #FF9735 -5%, rgba(255, 47, 134, 0) 49%), radial-gradient(134% 134% at 11% 123%, #FF83D1 0%, rgba(255, 52, 74, 0) 100%), linear-gradient(118deg, #FF007B 0%, #FF2C1A 56%);
    border-radius: 9999px;
    position: relative;
    border: none; /* 移除默认边框 */
    outline: none; /* 移除默认轮廓 */
    box-shadow: inset 0px -8.21px 12.31px 0px rgba(255, 255, 255, 0.3);
  }

  &-image-btn {
    @include flex-center;
    width: 180px;
    height: 48px;
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
    
    &-image {
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      object-position: center;
      display: block;
    }
    
    &:hover {
      opacity: 0.9;
      transition: opacity 0.2s ease;
    }
    
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
      transition: opacity 0.1s ease, transform 0.1s ease;
    }
  }

  &-close {
    @include flex-center;

    width: 30px;
    height: 30px;
    margin-top: 18px;
    &-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      pointer-events: none;
    }
  }
}

/* Loading 遮罩层样式 */
.loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}