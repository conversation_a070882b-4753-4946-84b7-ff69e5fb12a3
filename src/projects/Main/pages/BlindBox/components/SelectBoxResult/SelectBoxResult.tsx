import React, { useEffect, useState, useRef } from "react";

import styles from "./index.module.scss";
import { isNotEmptyString } from "@/projects/Main/utils/stringUtil";
import { motion, AnimatePresence } from "framer-motion";
import {
  reportSelectBoxResultMv,
  reportSelectBoxResultMc,
  BLINDBOX_CID,
} from "../../constants/report";
import { Toast } from "@roo/roo-b-mobile";
import { CardType, pageUrl } from "../../constants/BlindBoxConstants";
import {
  registerScreenShotShare,
  unRegisterScreenShotShare,
} from "@/projects/Main/utils/shareUtil";
import { getLottieAssetUrl, LOTTIE_ASSETS } from "@/projects/Main/utils/assetUtil";
import { usePreloadLottieData, useLottieAnimation } from "@/hooks/use-lottie-animation";

interface SelectBoxResultProps {
  img: string | undefined; // 展示兜底图
  spuName: string;
  content: string;
  decorationImages: string[];
  cardId: number; // 卡牌ID，用于埋点
  cardType: number;
  remainingChances: number; // 剩余抽奖次数
  handleBuyBoxClick: () => void;
  handlePickAgainClick: () => void;
  handleClose: (shouldTriggerAnimation?: boolean) => void;
}

function SelectBoxResult(props: SelectBoxResultProps) {
  const {
    spuName,
    content,
    img,
    decorationImages,
    cardId,
    cardType,
    remainingChances,
    handleBuyBoxClick,
    handlePickAgainClick,
    handleClose,
  } = props;

  const [isOpen, setIsOpen] = useState<boolean>(true);

  // 判断是否为隐藏款
  const isHidden = cardType === CardType.MYSTERY;

  // 预加载lottie动画数据
  const drawPopupAnimationUrl = getLottieAssetUrl('DRAW_POPUP');
  const handAnimationUrl = getLottieAssetUrl('HAND');
  const { loadedData, isLoading, error } = usePreloadLottieData([
    drawPopupAnimationUrl,
    handAnimationUrl
  ]);

  // 使用通用动画hook
  const popupAnimation = useLottieAnimation();
  const handAnimation = useLottieAnimation();

  // 确保组件挂载时isOpen为true
  useEffect(() => {
    console.log(`[DEBUG] 组件挂载，确保isOpen为true`);
    setIsOpen(true);
  }, []);

  // 监控isOpen状态变化
  useEffect(() => {
    console.log(`[DEBUG] isOpen状态变化: ${isOpen}`);
  }, [isOpen]);

  const [isPlayFlyAnimating, setIsPlayFlyAnimating] = useState<boolean>(false);
  const [isPlayHandAnimating, setIsPlayHandAnimating] =
    useState<boolean>(false);
  const [animationKey, setAnimationKey] = useState<number>(0);
  const [isReplaying, setIsReplaying] = useState<boolean>(false);

  // 页面曝光埋点
  useEffect(() => {
    reportSelectBoxResultMv();
  }, []);

  // 注册分享截屏功能
  // useEffect(() => {
  //   registerScreenShotShare(BLINDBOX_CID, pageUrl);
  //   backgroundSubscribe();
  //   disappearSubscribe();
  //   return () => {
  //     unRegisterScreenShotShare(BLINDBOX_CID, pageUrl);
  //     window.KNB.use("unsubscribe", {
  //       action: "background",
  //     });
  //     window.KNB.use("unsubscribe", {
  //       action: "disappear",
  //     });
  //   };
  // }, []);

  const backgroundSubscribe = () => {
    window.KNB.subscribe({
      action: "background",
      handle: () => {
        unRegisterScreenShotShare(BLINDBOX_CID, pageUrl);
      },
      success: (data: any) => {
        console.log("success:backgroundSubscribe");
      },
      fail: () => {
        console.log("fail:backgroundSubscribe");
      },
    });
  };
  const disappearSubscribe = () => {
    window.KNB.subscribe({
      action: "disappear",
      handle: () => {
        unRegisterScreenShotShare(BLINDBOX_CID, pageUrl);
      },
      success: (data: { subId: string | undefined }) => {
        console.log("success:disappearSubscribe");
      },
      fail: () => {
        console.log("fail:disappearSubscribe");
      },
    });
  };

  // 弹出动画
  const playPopupAnimation = () => {
    if (!popupAnimation.animationRef.current) return;

    // 使用预加载的数据
    const drawPopupAnimationData = loadedData.get(drawPopupAnimationUrl);
    if (drawPopupAnimationData) {
      console.log('[DEBUG] 使用预加载的弹出动画数据');
      popupAnimation.playAnimation(drawPopupAnimationData, () => {
        // 动画播放完成后的清理工作
        console.log('[DEBUG] 弹出动画播放完成');
      });
    } else {
      console.warn('[WARN] 弹出动画数据未预加载，使用备用加载方式');
      // 备用加载方式
      popupAnimation.loadLottieData(drawPopupAnimationUrl).then((data) => {
        popupAnimation.playAnimation(data);
      });
    }

    setTimeout(() => {
      setIsPlayFlyAnimating(true);
    }, 600);
  };

  const playHandAnimation = () => {
    if (!isPlayHandAnimating || !handAnimation.animationRef.current) return;

    // 使用预加载的数据
    const handAnimationData = loadedData.get(handAnimationUrl);
    if (handAnimationData) {
      console.log('[DEBUG] 使用预加载的手部动画数据');
      handAnimation.playAnimation(handAnimationData);
    } else {
      console.warn('[WARN] 手部动画数据未预加载，使用备用加载方式');
      // 备用加载方式
      handAnimation.loadLottieData(handAnimationUrl).then((data) => {
        handAnimation.playAnimation(data);
      });
    }
  };

  // 进入页面展示popup动画
  useEffect(() => {
    if (!isLoading && !error) {
      playPopupAnimation();
    }
  }, [isLoading, error]);

  useEffect(() => {
    if (isReplaying) {
      playPopupAnimation();
      setIsReplaying(false);
    }
  }, [isReplaying]);

  // fly动画
  useEffect(() => {
    if (!isPlayFlyAnimating) return;

    const timer = setTimeout(() => {
      setIsPlayHandAnimating(true);
    }, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [isPlayFlyAnimating]);

  useEffect(() => {
    if (isPlayHandAnimating) {
      playHandAnimation();
    }
  }, [isPlayHandAnimating]);

  const handleCloseClick = () => {
    setIsOpen(false);
  };

  const handlePickAgainClickWrapper = () => {
    // 触发"再抽一个"按钮点击埋点
    reportSelectBoxResultMc("再抽一个", cardId, content);
    //检查剩余次数
    if (remainingChances <= 0) {
      Toast.open({
        content: "今日次数已用完",
      });
      return;
    }
    handleCloseClick();
    // 添加延时等待退出动画播放完成（0.2秒）
    setTimeout(() => {
      handlePickAgainClick();
    }, 200);
  };

  const handleBuyBoxClickWrapper = () => {
    // 触发"下单奶茶领走他"按钮点击埋点
    reportSelectBoxResultMc("下单奶茶领走他", cardId, content);
    handleBuyBoxClick();
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 0.85,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  const contentVariants = {
    hidden: { scale: 0.4, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        damping: 5,
        stiffness: 20,
        mass: 0.5,
      },
    },
    exit: {
      scale: 0.8,
      opacity: 0,
      transition: { duration: 0.2, ease: [0, 0, 0.5, 1] },
    },
  };

  return (
    <AnimatePresence
      onExitComplete={() => {
        handleClose();
      }}
    >
      {isOpen && (
        <>
          <motion.div
            className={styles.overlay}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={overlayVariants}
          />
          <div
            key={`lottie-${animationKey}`}
            ref={popupAnimation.animationRef}
            className={styles["lottie-container"]}
          />
          <motion.div
            key={`content-${animationKey}`}
            className={styles.box}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
          >
            <div>
              <div className={styles.boxTitle}>{spuName}</div>
              <div className={styles.boxContentContainer}>
                <img
                  src="https://p0.meituan.net/cagent/13681d9a688c3f33282ab0dd688372391712.png"
                  className={styles.boxContentContainerTop}
                  aria-hidden="true"
                  //loading="lazy"
                />
                <div className={styles.boxContent}>{content}</div>
                <img
                  src="https://p0.meituan.net/cagent/c43ad2c1751a1ed4c97b3d8633e5dccb4779.png"
                  className={styles.boxContentContainerBottom}
                  aria-hidden="true"
                  //loading="lazy"
                />
              </div>
            </div>

            <div className={styles.boxImageContainer}>
              {isNotEmptyString(img) ? (
                <img
                  className={styles.boxImageContainerImg}
                  src={img}
                  alt="box"
                  //loading="lazy"
                />
              ) : null}
              {isPlayFlyAnimating && (
                <>
                  <img
                    key={`decor-1-${animationKey}`}
                    className={`${styles.imgTopRight} ${styles.animateTopRight}`}
                    src={
                      decorationImages?.[0] ||
                      "https://p0.meituan.net/cagent/20222690afeae901f1d40e298c1d8f4636124.png"
                    }
                    alt="装饰"
                    //loading="lazy"
                  />
                  <img
                    key={`decor-2-${animationKey}`}
                    className={`${styles.imgLeftCenter} ${styles.animateLeftCenter}`}
                    src={
                      decorationImages?.[1] ||
                      "https://p0.meituan.net/cagent/8b432247becab235f5c9155c2c3e3de1209868.png"
                    }
                    alt="装饰"
                    //loading="lazy"
                  />
                  <img
                    key={`decor-3-${animationKey}`}
                    className={`${styles.imgBottomRight} ${styles.animateBottomRight}`}
                    src={
                      decorationImages?.[2] ||
                      "https://p0.meituan.net/cagent/72b5abdb00d92829cd6770079b0914f7174568.png"
                    }
                    alt="装饰"
                    //loading="lazy"
                  />
                </>
              )}
              {/* 右上角隐藏款图片标签 */}
              {isHidden && (
                <img
                  src="https://p0.meituan.net/cagent/155fd14a2fa1cde6466901d3cd7712cf14962.png"
                  alt="隐藏款"
                  className={styles.hiddenTagImage}
                  //loading="lazy"
                />
              )}
            </div>

            <div className={styles.boxBtnGroup}>
              <div
                className={styles.boxAgainBtn}
                onClick={handlePickAgainClickWrapper}
              >
                再抽一个
              </div>
              <div
                className={styles.boxBuyBtn}
                onClick={handleBuyBoxClickWrapper}
              >
                下单奶茶领走他
                <img
                  src="https://p0.meituan.net/cagent/369356040f62fee6d8667d16d0f9ccba6098.png"
                  className={styles.boxBuyBtnDecorate}
                  alt=""
                  aria-hidden="true"
                  //loading="lazy"
                />
                {isPlayHandAnimating && (
                  <div
                    key={`hand-${animationKey}`}
                    className={styles.boxBuyBtnDecorateHand}
                    ref={handAnimation.animationRef}
                    aria-hidden="true"
                  />
                )}
              </div>
            </div>
            <button
              className={styles.boxClose}
              aria-label="关闭"
              onClick={handleCloseClick}
              type="button"
            >
              <img
                src="https://p0.meituan.net/cagent/a629fb6be7984c646ac129207362a0111361.png"
                alt="关闭"
                width={29}
                height={29}
                //loading="lazy"
              />
            </button>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

export default SelectBoxResult;
