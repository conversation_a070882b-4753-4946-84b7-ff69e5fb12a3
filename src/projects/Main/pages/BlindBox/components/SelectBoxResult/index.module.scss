.overlay {
  position: fixed;
  z-index: 997;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0);
}

/* lottie 动画容器：居中定位，层级位于 overlay 与 box 之间 */
.lottie-container {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 998;
  width: 100vw;
  height: auto;
  transform: translate(-50%, -50%);
  pointer-events: none;
  /* 性能优化 */
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  /* 硬件加速 */
  -webkit-transform: translate(-50%, -50%);
  -webkit-backface-visibility: hidden;
  /* 减少重绘 */
  contain: layout style paint;
  /* 新增：GPU加速 */
  -webkit-perspective: 1000px;
  perspective: 1000px;
  /* 新增：优化渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 扫描中弹窗
.box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0 40px 0;
  overflow-y: hidden;
  overflow-x: hidden;
  box-sizing: border-box;

  &-title {
    font-family: "MF YuanHei", sans-serif;
    font-size: 40px;
    font-weight: normal;
    font-style: italic;
    text-align: center;
    letter-spacing: 2px;
    background: linear-gradient(180deg, #fffef3 0%, #fffad7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    width: 100%;
  }

  &-content-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    width: 100%; // 或与.box-content一致
    margin-top: -17px; // 减少与title的距离

    &-top {
      width: 33px;
      height: 17px;
      pointer-events: none;
      z-index: 1;
      transform: translateY(8px) translateX(12px);
    }
    &-bottom {
      width: 126px;
      height: 21px;
      pointer-events: none;
      z-index: 1;
      transform: translateY(-12px);
    }
  }

  &-content {
    font-family: "MF YuanHei", sans-serif;
    font-size: 20px;
    font-weight: normal;
    text-align: center;
    letter-spacing: 2px;
    color: #ffffff;
    width: 100%;
  }

  &-image-container {
    width: 260px;
    height: 350px;
    background: url("https://p0.meituan.net/cagent/9ff184f64f6af6df395da0d153954ba041387.png")
      no-repeat center center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 24px;
    position: relative;
    overflow: visible;
    margin-top: 23px;

    &-img {
      // 图片左右裁边不一致
      border-radius: 6px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      box-shadow: 20.19px 20.19px 25.24px 0px rgba(0, 0, 0, 0.02);
    }

    .hidden-tag-image {
      position: absolute;
      top: 5%;
      left: -24px;
      width: 78px;
      height: 78px;
      z-index: 2;
      overflow: visible;
    }

    .img-top-right {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 78px;
      height: 78px;
      z-index: 2;
      overflow: visible;
      opacity: 0;
      transform: translate(-50%, -50%);
    }
    .img-left-center {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 112px;
      height: 112px;
      z-index: 2;
      overflow: visible;
      opacity: 0;
      transform: translate(-50%, -50%);
    }
    .img-bottom-right {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 115px;
      height: 115px;
      z-index: 2;
      overflow: visible;
      opacity: 0;
      transform-origin: center;
      transform: translate(-50%, -50%);
    }

    // 动画类
    .animate-top-right {
      animation: flyTopRight 0.5s ease-out forwards;
    }

    .animate-left-center {
      animation: flyLeftCenter 0.5s ease-out forwards;
    }

    .animate-bottom-right {
      animation: flyBottomRight 0.5s ease-out forwards;
    }
  }

  // 定义关键帧动画
  @keyframes flyTopRight {
    0% {
      opacity: 0;
      scale: 0.5;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      left: 50%; // 不变
      top: 50%; // 不变
      transform: translate(90px, -150px) scale(1) rotate(20deg); // 用transform做所有偏移
      opacity: 1;
    }
  }

  @keyframes flyLeftCenter {
    0% {
      opacity: 0;
      scale: 0.5;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      opacity: 1;
      scale: 1;
      left: 12px;
      top: 50%;
      transform: rotate(-25deg) translateX(-50%) scale(1) translateY(-50%);
    }
  }

  @keyframes flyBottomRight {
    0% {
      opacity: 0;
      scale: 0.5;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      left: 50%; // 不变
      top: 50%; // 不变
      transform: translate(60px, 60px) scale(1) rotate(20deg); // 用transform做所有偏移
      opacity: 1;
    }
  }

  &-menu {
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    &-img {
      // 图片左右裁边不一致
      border-radius: 6px;
      width: 85%;
      height: auto;
      object-fit: cover;
      box-shadow: 20.19px 20.19px 25.24px 0px rgba(0, 0, 0, 0.02);
    }
  }
  &-time {
    // margin-top: 12px;
    color: #f3f3f3;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    width: 100%;
  }
  &-buy-btn {
    display: flex;
    background: linear-gradient(93deg, #e7fa55 3%, #f4ffa3 85%);
    border: none;
    font-family: "PingFang SC", sans-serif;
    font-size: 17px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0px;
    color: #5f6720;
    border-radius: 5000px;
    height: 50px;
    align-items: center;
    justify-content: center;
    padding: 10px 22px;
    cursor: pointer;
    position: relative;
    transition:
      background 0.2s,
      color 0.2s,
      box-shadow 0.2s,
      transform 0.15s;

    &::before {
      content: "";
      position: absolute;
      top: -2.54px;
      left: -2.54px;
      right: -2.54px;
      bottom: -2.54px;
      background: linear-gradient(179deg, #fbffde 32%, #e1ff00 102%);
      border-radius: 5000px;
      z-index: -1;
    }
  }

  &-buy-btn-decorate {
    position: absolute;
    top: -20px;
    right: -8px;
    width: 67px;
    height: 27px;
    pointer-events: none;
    z-index: 2;
  }

  &-buy-btn-decorate-hand {
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 90px;
    height: 90px;
    pointer-events: none;
    z-index: 2;
  }

  &-again-btn {
    display: flex;
    border: 1px solid #e9fb61;
    font-family: "PingFang SC", sans-serif;
    font-size: 17px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0px;
    color: #e7fa5c;
    background: #333333;
    border-radius: 5000px;
    height: 50px;
    width: 133px;
    align-items: center;
    justify-content: center;
    padding: 10px 22px;
    cursor: pointer;
    transition:
      background 0.2s,
      color 0.2s,
      box-shadow 0.2s,
      transform 0.15s;
  }

  &-btn-group {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 16px;
    width: 100%;
    margin-top: 40px;
  }

  &-close {
    z-index: 10;
    background: none;
    border: none;
    padding: 0;
    margin-top: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition:
      opacity 0.2s,
      transform 0.2s;
    &:hover,
    &:focus {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}
