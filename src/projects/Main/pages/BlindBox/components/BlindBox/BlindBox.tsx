import React from "react";
import styles from "./index.module.scss";
import { IHomeInitData } from "@/projects/Main/services/blindbox/fetchBlindBox";
import BlindBoxItem from "../BlindBoxItem/BlindBoxItem";
import Reward from "../Reward";
import { ACTIVITY_END_TIME } from "../../constants/BlindBoxConstants";

interface BlindBoxProps {
  handleCollectionClick: (index: number) => void;
  cardCollectionInfo?: IHomeInitData["cardCollectionInfo"];
  collectedCount: number;
  totalCount: number;
}

function BlindBox(props: BlindBoxProps) {
  const { handleCollectionClick, cardCollectionInfo, collectedCount, totalCount } = props;

  // 展示的盲盒列表，如果没有数据则显示空列表
  const boxItems = cardCollectionInfo?.allCards || [];

  // 按照displayOrder排序
  const sortedBoxItems = [...boxItems].sort(
    (a, b) => a.displayOrder - b.displayOrder
  );

  return (
    <div className={styles.grid}>
      <div className={styles.gridContainer}>
        <div className={styles.gridTitle}>
          <img
            className={styles.gridTitleLine}
            src="https://p0.meituan.net/cagent/25be9c3c496e534be88623b87d946598230.png"
            alt=""
            loading="lazy"
            decoding="async"
            width="34"
            height="1"
          />
          <img
            className={styles.gridTitleXing}
            src="https://p0.meituan.net/cagent/0e5c49b4ec3ddc2b6f91edf09c950d13389.png"
            alt=""
            loading="lazy"
            decoding="async"
            width="12"
            height="12"
          />

          <div className={styles.gridTitleText}>
            {/* 我的盲盒&nbsp;{collectedCount}/{totalCount}{" "} */}
            我的收集进度
          </div>
          
          <img
            className={styles.gridTitleXing}
            src="https://p0.meituan.net/cagent/0e5c49b4ec3ddc2b6f91edf09c950d13389.png"
            alt=""
            loading="lazy"
            decoding="async"
            width="12"
            height="12"
          />
          <img
            className={styles.gridTitleLine}
            src="https://p0.meituan.net/cagent/5fd751479db6dd43cb3b64a48753d3bd232.png"
            alt=""
            loading="lazy"
            decoding="async"
            width="34"
            height="1"
          />
        </div>
        <div className={styles.gridContent}>
            已集齐 {collectedCount}/{totalCount} 款角色，每集齐1套可兑奖，转赠好友可加速进度
          </div>
        <div className={styles.gridContainerInner}>
          <Reward
            current={collectedCount}
            target={totalCount}
            targetTime={ACTIVITY_END_TIME}
          />

          <div className={styles.itemGrid}>
            {sortedBoxItems.map((item, index) => (
              <div key={item.cardId} className={styles.gridItem}>
                <BlindBoxItem
                  item={item}
                  index={index}
                  onClick={handleCollectionClick}
                />
              </div>
            ))}
          </div>
        </div>
        <div className={styles.gridContainerSpacer}></div>
      </div>
    </div>
  );
}

export default BlindBox;
