.grid {
  position: relative;
  top: -160px; //%可是相对父元素高度的50%
  height: 50vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  justify-items: center;
  z-index: 2;
  min-height: 100%;

  &-container {
    position: relative;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    min-height: 100%;
    height: 100%;
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    justify-items: center;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("https://p0.meituan.net/cagent/2e21478a641fbac17a6918a5747883fe70492.webp");
      background-size: 100% auto;
      background-position: top center;
      background-repeat: no-repeat;
      z-index: -1;
      border-top-left-radius: 30px;
      border-top-right-radius: 30px;
    }

    &::after {
      content: "";
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
      border-top-left-radius: 30px;
      border-top-right-radius: 30px;
      z-index: -2;
    }

    &-inner {
      border-radius: 30px;
      background: rgba(255, 255, 255);
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      justify-items: center;
    }

    &-spacer {
      min-height: 30px;
      height: 30px;
      width: 100%;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 12px;
    width: 100%;
    min-height: 18px;
    &-text {
      font-size: 18px;
      font-weight: 400;
      line-height: 18px;
      color: #3d4600;
      text-align: center;
      font-family: "MF YuanHei";
      letter-spacing: 0.06em;
      margin-left: 12px;
      margin-right: 12px;
    }
    &-line{
      width: 34px;
      height: 1px;
      margin: 0 6px;
    }
    
    &-xing{
      height: 12px;
      width: 12px;
    }
  }

  &-content {
    font-size: 12px;
    color: #555555;
    margin-bottom: 8px;
  }
}

.header {
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.subTitle {
  font-size: 14px;
  color: #666;
}

.itemGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px 15px;
  box-sizing: border-box;
}

.gridItem {
  width: 100%;
  display: flex;
}