import React, { useEffect, useState } from "react";

import styles from "./index.module.scss";
import { Toast, Loading } from "@roo/roo-b-mobile";
import ErrorPage from "../../../../components/ErrorPage";
import universalGoback from "@common/baseUtils/universalGoback";
import {
  CollectionStatus,
  pageUrl,
  BlindBoxDetailMode,
} from "./constants/BlindBoxConstants";
import {
  BLINDBOX_CID,
  reportPV,
  reportSelectBoxItemMc,
  reportSelectBoxRandomBtnMc,
} from "./constants/report";
import { getAllSearchParams, getSearchParams } from "../../utils/schemeUtil";

import {
  fetchBlindboxHomeInit,
  fetchDrawCard,
  fetchNewlyAcquiredCards,
  fetchGiveCardInfoByCard,
  IDrawCardResult,
  IHomeInitData,
  IDrawnCard,
  ICollectionCard,
  IGiveCardInfo,
} from "../../services/blindbox/fetchBlindBox";
import { logError } from "@wmfe/intelligent_common";

// 直接导入组件
import SelectBoxResult from "./components/SelectBoxResult";
import Header from "./components/Header";
import BlindBox from "./components/BlindBox";
import SelectBox from "./components/SelectBox";
import BlindBoxDetail from "./components/BlindBoxDetail";

function BlindBoxPage() {
  const urlParams = getAllSearchParams();
  console.log("BlindBoxPage - gift_token:", urlParams.gift_token);
  console.log("BlindBoxPage - entrance_type:", urlParams.entrance_type);
  const [homeData, setHomeData] = useState<IHomeInitData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  // 选择盲盒

  const [drawCardData, setDrawCardData] = useState<IDrawCardResult | null>(
    null
  );
  const [showResult, setShowResult] = useState<boolean>(false);

  // 底部已收藏
  const [collectionBoxIndex, setCollectionBoxIndex] = useState<number | null>(
    null
  );
  const [boxDetailPopup, setBoxDetailPopup] = useState<boolean>(false);

  // 新获得卡牌相关状态
  const [newlyAcquiredCardIds, setNewlyAcquiredCardIds] = useState<
    Array<number>
  >([]);
  const [currentNewCardIndex, setCurrentNewCardIndex] = useState<number>(0);
  const [isNewlyAcquiredMode, setIsNewlyAcquiredMode] =
    useState<boolean>(false);
  // 控制是否已经检查过新获得卡牌
  const [hasCheckedNewCards, setHasCheckedNewCards] = useState<boolean>(false);

  const [remainingChancesToday, setRemainingChancesToday] = useState<
    number | undefined
  >(undefined);

  // 控制SelectBoxItem关闭动画
  const [closingAnimationSlot, setClosingAnimationSlot] = useState<
    number | null
  >(null);

  // 标识结果弹窗是否从 BlindBoxItem 触发
  const [isResultFromBlindBoxItem, setIsResultFromBlindBoxItem] =
    useState<boolean>(false);

  // giftToken相关状态
  const [giftCardData, setGiftCardData] = useState<IGiveCardInfo | null>(null);
  const [showGiftCardDetail, setShowGiftCardDetail] = useState<boolean>(false);
  const [giftToken, setGiftToken] = useState<string>("");

  // 卡牌排序辅助函数
  const sortAllCards = (cards: ICollectionCard[]): ICollectionCard[] => {
    // 创建一个新数组以避免直接修改原数组
    const sortedCards = [...cards];
    sortedCards.sort((a, b) => {
      // 规则 1: 按 collectionStatus 降序排序
      if (a.collectionStatus !== b.collectionStatus) {
        return b.collectionStatus - a.collectionStatus;
      }
      // 规则 2: 按卡牌数量降序排序
      if (a.quantity !== b.quantity) {
        return b.quantity - a.quantity;
      }
      // 规则 3: 按 cardId 升序排序
      return a.cardId - b.cardId;
    });

    // 更新 displayOrder
    return sortedCards.map((card, index) => ({
      ...card,
      displayOrder: index + 1,
    }));
  };

  // 计算已收集卡牌数量的辅助函数
  const getCollectedCount = (): number => {
    return (
      homeData?.cardCollectionInfo?.allCards?.filter(
        (card) =>
          card.collectionStatus === CollectionStatus.OWNED ||
          card.collectionStatus === CollectionStatus.PENDING_CLAIM
      )?.length || 0
    );
  };

  // 计算总卡牌数量的辅助函数
  const getTotalCount = (): number => {
    return homeData?.cardCollectionInfo?.allCards?.length || 0;
  };

  // 隐藏导航栏方法
  const setHideNavigationBar = async (isHidden: boolean) => {
    if (!window.KNB) return;
    window.KNB.setNavigationBarHidden({
      flag: isHidden ? 1 : 0,
    });
  };

  // 沉浸式导航栏和页面曝光埋点
  useEffect(() => {
    // 设置页面标题
    document.title = "奶茶盲盒";

    // 隐藏导航栏
    setHideNavigationBar(true);

    // 上报页面曝光埋点
    try {
      // 获取入口类型参数，默认为4（其他）
      const entranceType = Number(getSearchParams("entrance_type"));
      console.log("entranceType:", entranceType);
      reportPV(entranceType);
    } catch (error) {
      console.error("页面曝光埋点上报失败:", error);
      // 埋点失败不影响主流程，使用默认值上报
      reportPV(4);
    }
  }, []);

  // 请求首页数据
  const fetchHomeData = async () => {
    try {
      setLoading(true);
      setError("");
      const data = await fetchBlindboxHomeInit();

      // 检查数据是否为空或存在错误信息
      if (!data) {
        setError("暂无数据，请稍后重试");
        return;
      }

      // 如果API返回的数据结构中有error字段，也进行处理
      if (data && typeof data === "object" && "error" in data && data.error) {
        setError((data.error as string) || "获取数据失败");
        return;
      }

      setHomeData(data);

      // 设置剩余抽卡次数
      if (remainingChancesToday === undefined) {
        setRemainingChancesToday(data.drawStatusToday.remainingChancesToday);
      }

      // 数据获取成功后，重新隐藏导航栏
      setHideNavigationBar(true);
    } catch (error) {
      console.error("获取盲盒数据出错:", error);
      // 优先使用接口返回的错误信息
      const errorMessage =
        error instanceof Error
          ? error.message
          : "网络异常，请检查网络连接后重试";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 检查新获得的卡牌
  const checkNewlyAcquiredCards = async () => {
    try {
      const newlyAcquiredCardIds = await fetchNewlyAcquiredCards();

      // 检查是否有新获得的卡牌
      if (newlyAcquiredCardIds && newlyAcquiredCardIds.length > 0) {
        // 保存新获得卡牌ID列表
        setNewlyAcquiredCardIds(newlyAcquiredCardIds);
        setCurrentNewCardIndex(0);
        setIsNewlyAcquiredMode(true);

        // 找到第一个新获得卡牌在allCards中的索引
        const firstNewCardId = newlyAcquiredCardIds[0];
        if (homeData?.cardCollectionInfo?.allCards) {
          const cardIndex = homeData.cardCollectionInfo.allCards.findIndex(
            (card) => card.cardId === firstNewCardId
          );
          if (cardIndex !== -1) {
            setCollectionBoxIndex(cardIndex);
            setBoxDetailPopup(true);
          }
        }
      }
    } catch (error) {
      console.error("获取新获得卡牌数据出错:", error);
      // 静默处理错误，不影响主流程
    }
  };

  // 处理giftToken
  const handleGiftToken = async (): Promise<boolean> => {
    const urlGiftToken = getSearchParams("gift_token");
    console.log("handleGiftToken url", window.location.href);
    console.log("giftToken:", urlGiftToken);
    if (!urlGiftToken) {
      // 为null、空、undefined、空字符串就返回false
      return false;
    }

    // 保存giftToken到状态中
    setGiftToken(urlGiftToken);

    try {
      const giftCardData = await fetchGiveCardInfoByCard(urlGiftToken);

      if (giftCardData) {
        setGiftCardData(giftCardData);
        setShowGiftCardDetail(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error("获取礼品卡牌数据出错:", error);
      // 错误处理已在 fetchGiveCardInfoByCard 中统一处理，这里不再重复显示 Toast
      return false;
    }
  };

  // 处理下一个新获得卡牌
  const handleNextNewCard = () => {
    const nextIndex = currentNewCardIndex + 1;

    if (nextIndex < newlyAcquiredCardIds.length) {
      // 还有下一个卡牌
      setCurrentNewCardIndex(nextIndex);
      const nextCardId = newlyAcquiredCardIds[nextIndex];

      if (homeData?.cardCollectionInfo?.allCards) {
        const cardIndex = homeData.cardCollectionInfo.allCards.findIndex(
          (card) => card.cardId === nextCardId
        );
        if (cardIndex !== -1) {
          setCollectionBoxIndex(cardIndex);
        }
      }
    } else {
      // 没有更多卡牌了，关闭弹窗并重置状态
      setBoxDetailPopup(false);
      setCollectionBoxIndex(null);
      setIsNewlyAcquiredMode(false);
      setNewlyAcquiredCardIds([]);
      setCurrentNewCardIndex(0);
    }
  };

  // 性能监控
  const monitorPerformance = () => {
    // 监控页面加载性能
    if ("performance" in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "navigation") {
            const navEntry = entry as PerformanceNavigationTiming;
            console.log("🚀 页面加载性能:", {
              DNS查询时间:
                navEntry.domainLookupEnd - navEntry.domainLookupStart,
              TCP连接时间: navEntry.connectEnd - navEntry.connectStart,
              首字节时间: navEntry.responseStart - navEntry.requestStart,
              DOM解析时间:
                navEntry.domContentLoadedEventEnd -
                navEntry.domContentLoadedEventStart,
              页面完全加载时间: navEntry.loadEventEnd - navEntry.loadEventStart,
              总加载时间: navEntry.loadEventEnd - navEntry.fetchStart,
            });
          }
        }
      });

      observer.observe({ entryTypes: ["navigation"] });
    }

    // 监控资源加载
    if ("performance" in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "resource") {
            const resourceEntry = entry as PerformanceResourceTiming;
            if (
              resourceEntry.name.includes(".js") &&
              resourceEntry.duration > 1000
            ) {
              console.warn("⚠️ 慢资源加载:", {
                资源: resourceEntry.name,
                加载时间: resourceEntry.duration + "ms",
                大小: resourceEntry.transferSize + "bytes",
              });
            }
          }
        }
      });

      resourceObserver.observe({ entryTypes: ["resource"] });
    }
  };

  useEffect(() => {
    monitorPerformance();
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0); // 页面加载时滚动到顶部
    // 获取盲盒首页数据
    fetchHomeData();
  }, []);

  // 当homeData加载完成后，先处理giftToken，再检查新获得的卡牌
  useEffect(() => {
    if (homeData && !hasCheckedNewCards) {
      // 优先处理giftToken
      handleGiftToken().then((hasGiftToken) => {
        // 如果没有giftToken，才检查新获得的卡牌
        if (!hasGiftToken) {
          checkNewlyAcquiredCards();
        }
        setHasCheckedNewCards(true);
      });
    }
  }, [homeData, hasCheckedNewCards]);

  /**
   * @description 使用抽中的卡牌数据更新 homeData 状态
   * @param {IDrawCardResult} drawnCardData - 抽卡接口返回的数据
   * @param {number} slot - 抽中的盲盒位置
   */
  const updateHomeDataWithDrawnCard = (drawnCardData: IDrawCardResult) => {
    // 1. 创建新的已抽取卡牌对象】
    const newDrawSlot = drawnCardData.drawSlot;
    const newCard: IDrawnCard = {
      cardId: drawnCardData.cardId,
      cardName: drawnCardData.cardName,
      imageUrl: drawnCardData.imageUrl,
      cardType: drawnCardData.cardType,
      recommendText: drawnCardData.recommendText,
      decorationImages: drawnCardData.decorationImages,
      gaodaJumpUrl: drawnCardData.gaodaJumpUrl,
      owned: drawnCardData.owned,
      drawSlot: newDrawSlot,
      drawStatus: CollectionStatus.DRAWN,
      // 补充 IDrawnCard 需要但 IDrawCardResult 中没有的字段
      inBoxImageUrl: drawnCardData.imageUrl,
      // 暂时没有顺序信息
      drawOrder: 0,
    };

    // 2. 更新 homeData 状态（乐观更新）
    setHomeData((prevData) => {
      if (!prevData) return null;

      // 更新今日已抽取卡牌列表
      const updatedDrawnTodayCards = [
        ...(prevData.drawStatusToday.drawnTodayCards || []),
      ];
      const existingCardIndex = updatedDrawnTodayCards.findIndex(
        (card) => card.drawSlot === newDrawSlot
      );
      if (existingCardIndex > -1) {
        updatedDrawnTodayCards[existingCardIndex] = newCard;
      } else {
        updatedDrawnTodayCards.push(newCard);
      }

      // 更新全量卡牌收藏列表的状态
      let updatedAllCards = [...prevData.cardCollectionInfo.allCards];
      const cardInCollectionIndex = updatedAllCards.findIndex(
        (card) => card.cardId === drawnCardData.cardId
      );

      if (cardInCollectionIndex > -1) {
        const cardToUpdate = updatedAllCards[cardInCollectionIndex];
        // 如果卡牌数量为0，说明是首次抽中，更新其状态为"已抽出"
        if (cardToUpdate.quantity === 0) {
          updatedAllCards[cardInCollectionIndex] = {
            ...cardToUpdate,
            collectionStatus: CollectionStatus.DRAWN,
          };
          // 只有在卡牌状态真实发生改变时(首次抽中)，才进行临时排序
          updatedAllCards = sortAllCards(updatedAllCards);
        }
      }

      return {
        ...prevData,
        drawStatusToday: {
          ...prevData.drawStatusToday,
          drawnTodayCards: updatedDrawnTodayCards,
          remainingChancesToday: drawnCardData.remainingChancesToday,
        },
        cardCollectionInfo: {
          ...prevData.cardCollectionInfo,
          allCards: updatedAllCards,
        },
      };
    });
  };

  /**
   * @description 请求抽卡接口并更新相关状态
   * @param {number} slot - 抽卡的位置
   */
  const requestDrawCard = async (slot: number) => {
    try {
      // 1. 请求抽卡接口
      const data = await fetchDrawCard(slot);

      // 2. 更新剩余抽卡次数
      updateHomeDataWithDrawnCard(data);
      setRemainingChancesToday(data.remainingChancesToday);

      return data;
    } catch (error) {
      console.error("获取盲盒数据出错:", error);
      // 错误处理已在 fetchDrawCard 中统一处理，这里不再重复显示 Toast
      throw error;
    } finally {
    }
  };

  // 处理已抽取卡牌的点击事件
  const handleDrawnCardClick = (slot: number, existingCard: IDrawnCard) => {
    reportSelectBoxItemMc(
      existingCard.cardId,
      slot,
      remainingChancesToday ?? 0,
      existingCard.drawStatus
    );

    // 如果该slot已经抽取过，根据归属状态决定显示不同弹窗
    if (existingCard.drawStatus === CollectionStatus.OWNED) {
      // 已归属，显示盲盒详情弹窗
      // 需要在cardCollectionInfo中找到对应的卡牌索引
      const cardIndex = homeData?.cardCollectionInfo?.allCards?.findIndex(
        (card) => card.cardId === existingCard.cardId
      );
      if (cardIndex !== undefined && cardIndex !== -1) {
        setCollectionBoxIndex(cardIndex);
        setBoxDetailPopup(true);
      }
    } else {
      // 未归属，显示抽取结果弹窗
      setDrawCardData({
        ...existingCard,
        remainingChancesToday: remainingChancesToday ?? 0,
      });
      setShowResult(true);
      // 设置标识，表明这是从 SelectBoxItem 触发的
      setIsResultFromBlindBoxItem(false);
    }
  };

  // 处理盲盒点击事件
  const handleBoxClick = (slot: number) => {
    if (slot !== 0) {
      // 检查该slot是否已经在今日抽取的卡牌中
      const existingCard = homeData?.drawStatusToday?.drawnTodayCards?.find(
        (card) => card.drawSlot === slot
      );

      if (existingCard) {
        handleDrawnCardClick(slot, existingCard);
        return;
      }
    }

    // 检查抽奖次数
    if ((remainingChancesToday ?? 0) <= 0) {
      Toast.open({
        content: "今日次数已用完",
      });
      return;
    }

    // 如果该slot未抽取过，则请求接口
    requestDrawCard(slot)
      .then((data) => {
        if (data && data.imageUrl && data.cardName) {
          setDrawCardData(data);
          setShowResult(true);
          // 设置标识，表明这是从 SelectBoxItem 触发的
          setIsResultFromBlindBoxItem(false);
          reportSelectBoxItemMc(
            data.cardId,
            slot,
            data.remainingChancesToday ?? 0,
            1 // 未获得
          );
        }
      })
      .catch((err) => {
        console.error("拆盲盒失败:", err);
      });
  };

  // 处理随机盲盒点击事件
  const handleRandomBoxClick = () => {
    // 随机选择一个盲盒
    handleBoxClick(0);
    reportSelectBoxRandomBtnMc();
  };

  const handlePickAgainClick = async () => {
    await handleCloseResult();
    handleBoxClick(0);
  };
  // 处理关闭结果弹窗
  const handleCloseResult = async (shouldTriggerAnimation: boolean = true) => {
    console.log(`shouldTriggerAnimation:${shouldTriggerAnimation}`);
    const slot = drawCardData?.drawSlot ?? 0;

    // 只有在需要触发动画时才设置关闭动画
    if (shouldTriggerAnimation) {
      setShowResult(false);
      setClosingAnimationSlot(slot);
      // 立即刷新数据，不需要等动画完成
      await fetchHomeData();
      // 动画完成时会通过 handleAnimationComplete 回调处理弹窗关闭
    } else {
      // 直接关闭弹窗，不触发动画
      setShowResult(false);
      setIsResultFromBlindBoxItem(false);
      // 为确保数据完全同步，仍然可以刷新数据
      await fetchHomeData();
    }
  };
  // 处理动画完成回调
  const handleAnimationComplete = () => {
    console.log("动画序列完成，关闭弹窗");
    setClosingAnimationSlot(null);
    console.log(`setClosingAnimationSlot:null`);
    setIsResultFromBlindBoxItem(false);
  };

  // 处理结果弹窗关闭的统一入口
  const handleResultClose = async (shouldTriggerAnimation?: boolean) => {
    // 如果明确指定了 shouldTriggerAnimation，则使用该值
    // 否则根据 isResultFromBlindBoxItem 状态决定：
    // - 如果是从 BlindBoxItem 点击触发的弹窗，则不触发动画
    // - 如果是从 SelectBoxItem 点击触发的弹窗，则触发动画
    const shouldAnimate = shouldTriggerAnimation ?? !isResultFromBlindBoxItem;
    await handleCloseResult(shouldAnimate);
  };

  // 处理盲盒收藏点击事件
  const handleCollectionClick = (index: number) => {
    const card = homeData?.cardCollectionInfo?.allCards?.[index];

    if (
      card?.collectionStatus === CollectionStatus.OWNED ||
      card?.collectionStatus === CollectionStatus.PENDING_CLAIM
    ) {
      setCollectionBoxIndex(index);
      setBoxDetailPopup(true);
      return;
    } else if (card?.collectionStatus === CollectionStatus.DRAWN) {
      // 检查该slot是否已经在今日抽取的卡牌中
      const drawnCard = homeData?.drawStatusToday?.drawnTodayCards?.find(
        (drawnCard) => drawnCard.cardId === card?.cardId
      );

      if (!drawnCard) {
        return;
      }

      // 未归属，显示抽取结果弹窗
      setDrawCardData({
        ...drawnCard,
        remainingChancesToday: remainingChancesToday ?? 0,
      });
      setShowResult(true);
      // 设置标识，表明这是从 BlindBoxItem 触发的
      setIsResultFromBlindBoxItem(true);
    } else {
      return;
    }
  };

  // 处理关闭盲盒详情弹窗
  const handleCollectionClose = () => {
    setBoxDetailPopup(false);
    setCollectionBoxIndex(null);
    // 如果是新获得模式，重置相关状态
    if (isNewlyAcquiredMode) {
      setIsNewlyAcquiredMode(false);
      setNewlyAcquiredCardIds([]);
      setCurrentNewCardIndex(0);
    }
  };

  // 处理关闭礼品卡牌详情弹窗
  const handleGiftCardClose = () => {
    setShowGiftCardDetail(false);
    setGiftCardData(null);
  };

  // 处理重试
  const handleRetry = () => {
    fetchHomeData();
  };

  // 如果有错误，显示错误页面
  if (error) {
    return (
      <ErrorPage
        error={error}
        handleRetry={handleRetry}
        title={"奶茶盲盒"}
        onBack={function (): void {
          universalGoback();
        }}
      />
    );
  }

  return (
    <div className={styles.container}>
      {!homeData && loading ? (
        <div className={styles.loadingContainer}>
          <Loading spinning iconType="vline" />
        </div>
      ) : (
        <>
          <Header />
          <SelectBox
            handleBoxClick={handleBoxClick}
            handleRandomBoxClick={handleRandomBoxClick}
            count={remainingChancesToday ?? 0}
            cardList={homeData?.drawStatusToday?.drawnTodayCards}
            closingAnimationSlot={closingAnimationSlot}
            onAnimationComplete={handleAnimationComplete} // 扫光动画播放完成
          />
          <BlindBox
            cardCollectionInfo={homeData?.cardCollectionInfo}
            handleCollectionClick={handleCollectionClick}
            collectedCount={getCollectedCount()}
            totalCount={getTotalCount()}
          />
          {showResult &&
            drawCardData &&
            drawCardData.imageUrl &&
            drawCardData.cardName && (
              <SelectBoxResult
                img={drawCardData.imageUrl}
                spuName={drawCardData.cardName}
                content={drawCardData.recommendText ?? "今日活力满满"}
                cardId={drawCardData.cardId}
                cardType={drawCardData.cardType}
                handleBuyBoxClick={() => {
                  const url = drawCardData.gaodaJumpUrl;
                  console.log(url);
                  window.KNB.openPage({
                    url,
                    fail: function (error: any) {
                      console.warn(error);
                      Toast.open({
                        content: "跳转失败，请稍后再试",
                      });
                      logError(`${error} | url: ${url}`, "router fail");
                    },
                  });
                }}
                handlePickAgainClick={handlePickAgainClick}
                handleClose={handleResultClose}
                decorationImages={drawCardData.decorationImages}
                remainingChances={remainingChancesToday ?? 0}
              ></SelectBoxResult>
            )}
          {boxDetailPopup &&
            collectionBoxIndex !== null &&
            homeData?.cardCollectionInfo?.allCards?.[collectionBoxIndex] &&
            (() => {
              // 提取当前卡牌数据，避免重复访问
              const currentCard =
                homeData.cardCollectionInfo.allCards[collectionBoxIndex];

              // 计算是否为最后一张新获得卡牌
              const isLastNewCard =
                isNewlyAcquiredMode &&
                newlyAcquiredCardIds.length > 0 &&
                currentNewCardIndex === newlyAcquiredCardIds.length - 1;

              return (
                <BlindBoxDetail
                  spuName={currentCard.cardName}
                  collectedCount={getCollectedCount()}
                  totalCount={getTotalCount()}
                  img={currentCard.rotateWebpUrl}
                  shareImg={currentCard.shareImageUrl}
                  shareOriginImg={currentCard.shareOriginImageUrl}
                  cardType={currentCard.cardType}
                  cardStatus={currentCard.collectionStatus}
                  giftToken={currentCard.giftToken} //赠送礼物时的唯一token,仅当collectionStatus为转赠中有值
                  handleClose={handleCollectionClose}
                  mode={
                    isNewlyAcquiredMode
                      ? BlindBoxDetailMode.NEWLY_ACQUIRED
                      : BlindBoxDetailMode.NORMAL
                  }
                  isLast={isLastNewCard}
                  onNext={handleNextNewCard}
                  cardId={currentCard.cardId}
                  onRefresh={fetchHomeData}
                />
              );
            })()}
          {showGiftCardDetail &&
            giftCardData &&
            (() => {
              const cardInfo = giftCardData.cardInfo;
              const giverUserName = giftCardData.giverUserName;
              return (
                <BlindBoxDetail
                  spuName={cardInfo.cardName}
                  collectedCount={getCollectedCount()}
                  totalCount={getTotalCount()}
                  img={giftCardData.rotateWebpUrl}
                  cardType={cardInfo.cardType}
                  handleClose={handleGiftCardClose}
                  mode={BlindBoxDetailMode.RECEIVE}
                  giftToken={giftToken}
                  isLast={true}
                  onNext={() => {}}
                  cardId={cardInfo.cardId}
                  onRefresh={fetchHomeData}
                  giverUserName={giverUserName}
                  shareImg={cardInfo.imageUrl}
                  shareOriginImg={cardInfo.imageUrl}
                  cardStatus={0}
                />
              );
            })()}
        </>
      )}
    </div>
  );
}

export default BlindBoxPage;
