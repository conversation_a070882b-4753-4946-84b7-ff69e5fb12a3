.container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://p0.meituan.net/cagent/a6d8832f89c1d444436118669d17b7cb146074.webp') no-repeat;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 17px;
  // 隐藏滚动条（兼容主流浏览器）
  overflow-y: auto;
  overflow-x: hidden; // 防止水平滚动
  -ms-overflow-style: none; // IE 10+
  // 防止长按时出现选择文本和拖拽行为
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  // 防止触摸时的默认行为
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  // 禁止下拉刷新和overscroll效果
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
  // 防止弹性滚动
  -webkit-overscroll-behavior: none;
  -moz-overscroll-behavior: none;
  -ms-overscroll-behavior: none;
  &::-webkit-scrollbar {
    display: none; // Chrome/Safari
  }
  
  // 防止所有子元素的长按选择
  * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  // 允许输入框和文本区域的选择
  input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}

.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('https://p0.meituan.net/cagent/a6d8832f89c1d444436118669d17b7cb146074.webp') no-repeat;
  background-size: cover;
  background-position: center;
  z-index: 1000;
}
