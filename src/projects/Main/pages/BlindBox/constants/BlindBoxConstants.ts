/**
 * 盲盒卡片类型枚举
 */
export enum CardType {
  /** 普通款 */
  NORMAL = 1,
  /** 隐藏款 */
  MYSTERY = 2,
}

export enum DrawRecordStatus {
  /** 已抽出 */
  DRAWN = 2,
  /** 已领取 */
  COLLECTED = 3,
}

/**
 * 盲盒卡片收集状态枚举
 */
export enum CollectionStatus {
  /** 未获得 */
  NOT_OBTAINED = 1,
  /** 已抽出 */
  DRAWN = 2,
  /** 已获得 */
  OWNED = 3,
  /** 转赠中 */
  PENDING_CLAIM = 4,
}

export const pageUrl =
"imeituan://www.meituan.com/takeout/browser?notitlebar=1&inner_url=https%3A%2F%2Fai.waimai.meituan.com%2Fcagent%2Fblindbox%3Fnotitlebar%3D1%26future%3D2%26entrance_type%3D1";

/**
 * 盲盒详情弹窗模式枚举
 */
export enum BlindBoxDetailMode {
  /** 新获得卡牌模式 */
  NEWLY_ACQUIRED = 'newly_acquired',
  /** 接收卡片模式 */
  RECEIVE = 'receive',
  /** 普通模式 */
  NORMAL = 'normal',
}

/**
 * 活动结束时间
 * 活动时间：2025年7月11日00:00 - 2025年8月31日23:59
 * 
 * 服务端下发格式建议：
 * 1. ISO 8601格式: "2025-08-31T23:59:59.000Z"
 * 2. Unix时间戳(毫秒): 1756742399000
 * 3. 结构化对象: { year: 2025, month: 8, day: 31, hour: 23, minute: 59, second: 59 }
 */
export const ACTIVITY_END_TIME = new Date('2025-08-31T23:59:59');