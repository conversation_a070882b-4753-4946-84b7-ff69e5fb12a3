import { lxReport } from "@/.common";

export const BLINDBOX_CID = "c_waimai_3vlomj3y";

export const BLINDBOX_BID = {
  shareH5BtnMc: "b_waimai_g3prn4td_mc", //分享页面btn
  shareDialogMv: "b_waimai_c4zlsqyg_mv", // 分享页面弹窗
  shareDialogBtnMc: "b_waimai_c4zlsqyg_mc", //分享页面点击分享按钮
  ruleBtnMc: "b_waimai_cdxhb3t4_mc",
  selectBoxItemMc: "b_waimai_ftels5rn_mc", // 手动拆盲盒点击事件
  selectBoxRandomBtnMc: "b_waimai_o3jk55t5_mc", //随机拆盲盒
  selectBoxResultMv: "b_waimai_kinp09lx_mv",
  selectBoxResultMc: "b_waimai_kinp09lx_mc",
  blindBoxDetailMv: "b_waimai_0ez5w425_mv",
  blindBoxDetailMc: "b_waimai_0ez5w425_mc",
  blindBoxItemMv: "b_waimai_0edaimmf_mc",
  shareImageMv: "b_waimai_ksohzef0_mv",
  shareImageMc: "b_waimai_ksohzef0_mc",
};

// entrance_type,入口类型（1=外卖搜索，2=趋势播报，3奶茶搜索结果页，4=其他
export const reportPV = (entrance_type: number) => {
  lxReport.reportPV({
    cid: BLINDBOX_CID,
    customData: { entrance_type: entrance_type },
  });
};

// 分享类型，1=微信好友，2=朋友圈，3=其他
export const reportShareH5BtnMc = () => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.shareH5BtnMc,
    cid: BLINDBOX_CID,
  });
};

export const reportRuleBtnMc = () => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.ruleBtnMc,
    cid: BLINDBOX_CID,
  });
};
//
export const reportSelectBoxItemMc = (
  card_id: number,
  slot: number,
  remain_chance: number,
  status: number
) => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.selectBoxItemMc,
    cid: BLINDBOX_CID,
    customData: {
      card_id: card_id,
      index: slot,
      remain_chance: remain_chance,
      status: status,
    },
  });
};

export const reportSelectBoxRandomBtnMc = () => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.selectBoxRandomBtnMc,
    cid: BLINDBOX_CID,
  });
};

export const reportSelectBoxResultMv = () => {
  lxReport.reportMV({ bid: BLINDBOX_BID.selectBoxResultMv, cid: BLINDBOX_CID });
};

export const reportSelectBoxResultMc = (
  button_name: string,
  card_id: number,
  keyword: string
) => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.selectBoxResultMc,
    cid: BLINDBOX_CID,
    customData: {
      button_name: button_name,
      card_id: card_id,
      keyword: keyword,
    },
  });
};

export const reportBlindBoxDetailMv = () => {
  lxReport.reportMV({ bid: BLINDBOX_BID.blindBoxDetailMv, cid: BLINDBOX_CID });
};

export const reportBlindBoxDetailMc = (
  button_name: string,
  card_id: number,
  collected_number: number
) => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.blindBoxDetailMc,
    cid: BLINDBOX_CID,
    customData: {
      button_name: button_name,
      card_id: card_id,
      collected_number: collected_number,
    },
  });
};

export const reportBlindBoxItemMc = (card_id: number, status: number) => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.blindBoxItemMv,
    cid: BLINDBOX_CID,
    customData: {
      card_id: card_id,
      status: status,
    },
  });
};

export const reportShareImageMv = () => {
  lxReport.reportMV({ bid: BLINDBOX_BID.shareImageMv, cid: BLINDBOX_CID });
};

export const reportShareImageMc = (card_id: number, share_type: number) => {
  lxReport.reportMC({
    bid: BLINDBOX_BID.shareImageMc,
    cid: BLINDBOX_CID,
    customData: {
      card_id: card_id,
      share_type: share_type,
    },
  });
};
