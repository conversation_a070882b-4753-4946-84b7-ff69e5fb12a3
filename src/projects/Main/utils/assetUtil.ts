/**
 * 静态资源路径管理工具
 * 用于处理不同环境下的静态资源路径问题
 */

// 云存储基础路径
const CLOUD_STORAGE_BASE = "https://s3plus-bj02.sankuai.com/wmcagent";

// 构建Lottie动画资源URL
export const buildLottieUrl = (filename: string): string => {
  return `/lottie/${filename}`;
};

// 预定义的Lottie动画资源
export const LOTTIE_ASSETS = {
  DRAW_POPUP: "draw-popup-lottie.json",
  HAND: "hand-lottie.json",
  COLLECTION_POPUP: "collection-popup-lottie.json",
  SELECT_BOX_ITEM: "select-box-item.json",
} as const;

// 云存储的Lottie动画资源映射
export const CLOUD_LOTTIE_ASSETS = {
  DRAW_POPUP: `${CLOUD_STORAGE_BASE}/draw-popup-lottie.json`,
  HAND: `${CLOUD_STORAGE_BASE}/hand-lottie.json`,
  COLLECTION_POPUP: `${CLOUD_STORAGE_BASE}/collection-popup-lottie.json`,
  SELECT_BOX_ITEM: `${CLOUD_STORAGE_BASE}/select-box-item.json`,
} as const;

// 开发环境代理的Lottie动画资源映射
export const PROXY_LOTTIE_ASSETS = {
  DRAW_POPUP: `/wmcagent/draw-popup-lottie.json`,
  HAND: `/wmcagent/hand-lottie.json`,
  COLLECTION_POPUP: `/wmcagent/collection-popup-lottie.json`,
  SELECT_BOX_ITEM: `/wmcagent/select-box-item.json`,
} as const;

// 判断是否为生产环境
const isProduction = (): boolean => {
  return (
    typeof window !== "undefined" &&
    window.location.hostname !== "localhost" &&
    window.location.hostname !== "127.0.0.1" 
  );
};

// 获取Lottie动画资源的完整URL
export const getLottieAssetUrl = (
  assetKey: keyof typeof LOTTIE_ASSETS
): string => {
  // 在生产环境中使用云存储路径，在开发环境中使用代理路径
  if (isProduction()) {
    return CLOUD_LOTTIE_ASSETS[assetKey];
  }
  return PROXY_LOTTIE_ASSETS[assetKey];
};
