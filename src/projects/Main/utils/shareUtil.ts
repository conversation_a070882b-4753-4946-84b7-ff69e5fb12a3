import { Toast } from "@roo/roo-b-mobile";
import { env, logError, wx } from "@wmfe/intelligent_common";

export const appShareH5 = (data: {
  title: string;
  desc: string;
  image: string;
  url?: string;
  onSuccess?: (result: any) => void;
  onFail?: (err: any) => void;
}) => {
  try {
    if (!window.KNB) {
      return;
    }
    window.KNB.ready(function () {
      window.KNB.share({
        ...data,
        // 如果没有传入url，使用当前页面URL作为默认值
        url: data.url || window.location.href,
        // 外卖 读取此参数
        channel: [0, 1], // 0是微信好友, 1是朋友圈
        // 美团 读取此参数
        channelV2s: [10, 11], // 10是微信好友， 11是朋友圈
        success: function (result: any) {
          console.log(result);
          Toast.open({
            content: "分享成功",
          });
          // 调用成功回调
          data.onSuccess?.(result);
        },
        fail(err: any) {
          console.log(JSON.stringify(err));
          Toast.open({
            content: "分享失败，请稍后再试",
          });
          logError(JSON.stringify(err),"knb fail");
          // 调用失败回调
          data.onFail?.(err);
        },
      });
    });
  } catch (e) {
    // do nothing
  }
};

export const appShareImage = (thumbPic:string,imageUrl: string, channel: any) => {
  try {
    if (!window.KNB) {
      return;
    }
    window.KNB.ready(function () {
      window.KNB.shareImage({
        sceneToken: "dj-82aba19c37eff15d", // 受隐私合规中长期方案影响，如果分享的是本地图片，新增sceneToken字段，详情请咨询外卖平台
        thumbPic: thumbPic,
        picQuality: 100,
        image: imageUrl,
        channel: channel, // 0是微信好友, 1是朋友圈，KNB.shareImage.WECHAT_FRIENDS代表分享至微信好友， KNB.shareImage.WECHAT_TIMELINE代表分享至朋友圈，
        success: function (result: any) {
          Toast.open({
            content: "分享成功",
          });
          console.log(result);
        },
        fail: function (err: any) {
          Toast.open({
            content: "分享失败，请稍后再试",
          });
          logError(JSON.stringify(err),"knb fail");
        },
      });
    });
  } catch (e) {
    // do nothing
  }
};

export const appDownloadImage = (imageUrl: string) => {
  try {
    if (!window.KNB) {
      return;
    }
    window.KNB.ready(function () {
      window.KNB.downloadImage({
        sceneToken: "dj-82aba19c37eff15d", // 受隐私合规中长期方案影响，新增sceneToken字段，安卓权限为 Storage iOS权限为 Photos
        imageUrl: imageUrl, //需要下载的图片资源URL
        success: function (data: any) {
          Toast.open({content:'保存成功'});
        },
        fail: function (err: any) {
          Toast.open({
            content: "保存失败，请检查系统权限",
          });
          logError(JSON.stringify(err),"knb fail");
        },
      });
    });
  } catch (e) {
    // do nothing
  }
};

export const appShareChannel = (
  title: string,
  desc: string,
  content: string,
  image: string,
  url: string,
  channel: any
) => {
  try {
    if (!window.KNB) {
      return;
    }
    window.KNB.ready(function () {
      window.KNB.share({
        sceneToken: "dj-82aba19c37eff15d", // 受隐私合规中长期方案影响，如果分享的是本地图片，新增sceneToken字段，权限说明参考如下表格
        //标题
        title: title,
        // 分享描述
        desc: desc,
        //只出现在分享到朋友圈，如果未提供content，则读取【title】+desc
        content: content,
        //分享图标，建议分享图片尺寸 100X100~120X120, 大小不超过32K
        image: image,
        //分享链接，分享出去时share SDK会进行short url处理
        url: url,
        // 传给客户端的也是数组; 如果只提供了一个渠道，直接调起分享即可
        // 无需弹出modal窗口（单一渠道直接调起美团实现，见下方表格）
        channel: [channel],
        success: function (result: any) {
          console.log(result);
          Toast.open({
            content: "分享成功",
          });
        },
        //用户取消了分享执行fail函数（美团）
        fail: function (res: any) {
          console.log(JSON.stringify(res));
          Toast.open({
            content: "保存失败，请检查系统权限",
          });
          logError(JSON.stringify(res),"knb fail");
        },
      });
    });
  } catch (e) {
    // do nothing
  }
};

export const registerScreenShotShare = (cid: string, pageUrl: string) => {
  try {
    if (!window.KNB) {
      console.warn("⚠️ window.KNB 不存在，无法注册截屏分享功能");
      return;
    }
    window.KNB.use("share.registerScreenShotShare", {
      buName: "餐饮外卖", // 业务名称
      cid: cid, // 页面埋点cid，建议唯一
      pageUrlString: pageUrl, // 触发截屏页面的imeituan链接
      urlString: "", // 业务主链,可以为空，拼接二维码的
      addQRCode: false,
      customQR: false,
      qrCodeDesc: "长按或扫描二维码 查看详情",
      qrCodeSubtitle: "长按或扫描二维码 查看详情",
      success: (result: any) => {
        console.log("✅ 截屏分享注册成功", result);
      },
      fail: (err: any) => {
        console.error("❌ 截屏分享注册失败, err:", err);
      },
    });
  } catch (error) {
    console.error("注册截屏分享功能时发生错误:", error);
  }
};

export const unRegisterScreenShotShare = (cid: string, pageUrl: string) => {
  try {
    if (!window.KNB) {
      console.warn("⚠️ window.KNB 不存在，跳过截屏分享清理");
      return;
    }
    window.KNB.use("share.unRegisterScreenShotShare", {
      buName: "餐饮外卖",
      cid: cid,
      pageUrlString: pageUrl,
      urlString: "",
      success: (result: any) => {
        console.log("✅ 移除截屏分享成功", result);
      },
      fail: (err: any) => {
        console.error("❌ 移除截屏分享失败, err:", err);
      },
    });
  } catch (error) {
    console.error("移除截屏分享功能时发生错误:", error);
  }
};

export const appSharePanel = () => {
  try {
    if (!window.KNB) {
      return;
    }
    window.KNB.ready(function () {
      window.KNB.share({
        title: "分享标题",
        desc: "分享描述",
        image:
          "https://img.meituan.net/msmerchant/de8a433be44cb36088b170490d6173db323302.jpg@380w_214h_1e_1c", //冰淇淋
        url: "http://e.platform.proxy.b.waimai.test.sankuai.com/main/bindbox", // 点击跳转的URL
        channel: [
          window.KNB.share.WECHAT_FRIENDS,
          window.KNB.share.WECHAT_TIMELINE,
        ], //分享渠道 v1，只支持一种渠道，如果传多渠道会调起全面板
        //自定义分享渠道，自美团12.5.200后将只展示列表中的渠道,其中8192为海报渠道，
        //开启自定义功能需要检查horn开关中titans_bridge_switch下share_rollback为false （该开关对线上无影响）
        channelV2s: [
          window.KNB.share.V2.WeixinFriends,
          window.KNB.share.V2.Copy,
          window.KNB.share.V2.WeixinTimeline,
          8192,
        ],
        panel: {
          //口令
          cid: "c_gx3sygn7", // 业务cid
          pwTemplateIndex: "1", // 模板index
          pwTemplateKey: "ZWE5MGVmNWYt", //模板key
          pwConfigBtn: "立即给我关闭", //口令底部按钮标题
          passwordURL: "https://i.meituan.com", //落地页地址
          activityTitleString: "活动", // 活动标题
          //活动图片，iOS端是activityImageURLString，Android端是image
          activityImageURLString:
            "https://assets.tmecosys.com/image/upload/t_web767x639/img/recipe/ras/Assets/9469D8A3-4255-46AF-98ED-8E08E0DB59CA/Derivates/5e21d30d-3e69-4400-9663-0b9666019d0e.jpg", //面包
          jumpUrl: "www.bilibili.com", // 落地页地址
          //海报渠道参数
          posterConfig: {
            posterImageString:
              "https://p0.meituan.net/travelcube/50a6c172b34caf1aa048fffff2162732391609.jpg@80q", //旅店游乐场 海报默认图
            posterTitle: "海报标题", // 默认为"推荐一个好物给你，请查收"
            logoImageUrl:
              "http://p0.meituan.net/shaitu/f81182057279931e3d2bfdf5db3a907090931.jpg", //种树
            qrCodeDesc: "长按或扫描二维码 查看详情", // 默认为 "长按或扫描二维码 查看详情"
            qrCodeJumpUrl: "https://www.baidu.com",
          },
          addQRCode: 1, // 1拼接二维码，0不拼接
          posterEnable: 1, // 1打开海报功能，0不打开
        },

        success: function (data: any) {
          console.log(data);
          Toast.open({
            content: "分享成功",
          });
          //zhixing1
        },
        fail: function (res: any) {
          console.log(JSON.stringify(res));
        },
      });
    });
  } catch (e) {
    // do nothing
  }
};

// 微信小程序中只能分享微信小程序卡片
export const setupWXAppShareConfig = (data: {
  title: string;
  image: string;
  url: string;
}) => {
  if (env.isWX) {
    let path = getWXAppH5Path(data.url);
    try {
      wx.miniProgram.postMessage({
        data: {
          type: "share", // type设为share
          title: data.title,
          path: path, // 分享的小程序路径
          imageUrl: data.image,
        },
      });
    } catch (e) {
      // do nothing
    }
  }
};

// 美团小程序的webview容器
export const WXAPP_H5_PREFIX =
  "/pages/web-view/web-view?type=DIRECT&webviewUrl=";
export const getWXAppH5Path = (originH5Url: string) => {
  let encodeUrl = encodeURIComponent(originH5Url);
  let ret = WXAPP_H5_PREFIX + encodeUrl;
  return ret;
};

// 判断设备是否为 微信
export const isWeixin = (): boolean => {
  const ua = navigator.userAgent.toLowerCase();
  return ua.includes('micromessenger') || ua.includes('wechat');
};