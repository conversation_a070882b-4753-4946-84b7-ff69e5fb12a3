export function getAllSearchParams() {
  try {
    const pageUrl = new URL(location.href);
    console.log('getAllSearchParams - 当前URL:', location.href);
    const commonData: any = {};
    Array.from(pageUrl.searchParams.keys()).forEach(key => {
        const value = pageUrl.searchParams.get(key);
        commonData[key] = value;
        console.log(`getAllSearchParams - ${key}: ${value}`);
    });
    console.log('getAllSearchParams - 最终结果:', commonData);
    return commonData;
  } catch (error) {
    console.error('getAllSearchParams 错误:', error);
    return {};
  }
}

export const searchParams = getAllSearchParams();

export function getSearchParams(searchKey: string) {
  try {
    const pageUrl = new URL(location.href);
    console.log('getSearchParams - 当前URL:', location.href);
    console.log('getSearchParams - 查找参数:', searchKey);
    console.log('getSearchParams - 所有参数:', Array.from(pageUrl.searchParams.entries()));
    
    // 遍历所有参数，去除空格后比较
    const foundEntry = Array.from(pageUrl.searchParams.entries()).find(([key, value]) => {
      return key.trim() === searchKey.trim();
    });
    
    if (foundEntry) {
      console.log('getSearchParams - 找到匹配参数:', foundEntry[0], '值:', foundEntry[1]);
      return foundEntry[1] || '';
    }
    
    console.log('getSearchParams - 未找到匹配参数');
    return '';
  } catch (error) {
    console.error('getSearchParams 错误:', error);
    return '';
  }
}

export const updateSearchParams = (searchParam: any, pushHistoryStack = true) => {
// Split the URL to get the query string part
const hrefArray = window.location.href.split('?');
const queryStr = hrefArray[1] || ''; // Use empty string if no existing query

// Convert query string into a parameter object
const queryParams = queryStr.split('&').reduce((params: any, param) => {
  const [key, value] = param.split('=');
  if (key) {
    params[key] = decodeURIComponent(value || '');
  }
  return params;
}, {});

// Merge existing parameters with new parameters
const newQueryParams = { ...queryParams, ...searchParam };

// Build new query string
const newQueryStr = Object.entries(newQueryParams)
  .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
  .join('&');

// Build new URL
const newUrl = `${hrefArray[0]}?${newQueryStr}`;

// Update browser history
if (pushHistoryStack) {
  window.history.pushState(newQueryParams, '', newUrl);
} else {
  window.history.replaceState({}, '', newUrl);
}
}
