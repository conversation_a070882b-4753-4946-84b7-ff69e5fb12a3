import React, { Suspense } from "react";
import "@roo/roo-multiplex/es/css";

// 动态导入主页面组件
const BlindBoxPage = React.lazy(() => import("./pages/BlindBox"));
const AddressEditPage = React.lazy(() => import("./pages/BlindBox/components/AddressEdit"));

// 加载占位符组件
const LoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh',
    fontSize: '16px',
    color: '#666'
  }}>
    加载中...
  </div>
);

function App() {
  // 获取URL参数来判断当前页面
  const urlParams = new URLSearchParams(window.location.search);
  const page = urlParams.get('page');

  // 根据页面参数渲染不同组件
  const renderPage = () => {
    switch (page) {
      case 'address-edit':
        return <AddressEditPage />;
      default:
        return <BlindBoxPage />;
    }
  };

  return (
    <Suspense fallback={<LoadingFallback />}>
      {renderPage()}
    </Suspense>
  );
}

export default App;
