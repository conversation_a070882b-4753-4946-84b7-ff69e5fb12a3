export interface AddressOption {
  value: string;
  label: string;
  children?: AddressOption[] | null;
}

export const ADDRESS_DATA: AddressOption[] = [
  {
    value: "110000",
    label: "北京市",
    children: [
      {
        value: "110100",
        label: "北京市",
        children: [
          { value: "110117", label: "平谷区", children: null },
          { value: "110111", label: "房山区", children: null },
          { value: "110114", label: "昌平区", children: null },
          { value: "110116", label: "怀柔区", children: null },
          { value: "110118", label: "密云区", children: null },
          { value: "110119", label: "延庆区", children: null },
          { value: "110101", label: "东城区", children: null },
          { value: "110102", label: "西城区", children: null },
          { value: "110105", label: "朝阳区", children: null },
          { value: "110106", label: "丰台区", children: null },
          { value: "110107", label: "石景山区", children: null },
          { value: "110108", label: "海淀区", children: null },
          { value: "110109", label: "门头沟区", children: null },
          { value: "110112", label: "通州区", children: null },
          { value: "110113", label: "顺义区", children: null },
          { value: "110115", label: "大兴区", children: null }
        ]
      }
    ]
  },
  {
    value: "120000",
    label: "天津市",
    children: [
      {
        value: "120100",
        label: "天津市",
        children: [
          { value: "120101", label: "和平区", children: null },
          { value: "120102", label: "河东区", children: null },
          { value: "120103", label: "河西区", children: null },
          { value: "120104", label: "南开区", children: null },
          { value: "120105", label: "河北区", children: null },
          { value: "120106", label: "红桥区", children: null },
          { value: "120110", label: "东丽区", children: null },
          { value: "120111", label: "西青区", children: null },
          { value: "120112", label: "津南区", children: null },
          { value: "120113", label: "北辰区", children: null },
          { value: "120114", label: "武清区", children: null },
          { value: "120115", label: "宝坻区", children: null },
          { value: "120116", label: "滨海新区", children: null },
          { value: "120117", label: "宁河区", children: null },
          { value: "120118", label: "静海区", children: null },
          { value: "120119", label: "蓟州区", children: null }
        ]
      }
    ]
  },
  {
    value: "130000",
    label: "河北省",
    children: [
      {
        value: "130100",
        label: "石家庄市",
        children: [
          { value: "130102", label: "长安区", children: null },
          { value: "130104", label: "桥西区", children: null },
          { value: "130105", label: "新华区", children: null },
          { value: "130107", label: "井陉矿区", children: null },
          { value: "130108", label: "裕华区", children: null },
          { value: "130109", label: "藁城区", children: null },
          { value: "130110", label: "鹿泉区", children: null },
          { value: "130111", label: "栾城区", children: null },
          { value: "130121", label: "井陉县", children: null },
          { value: "130123", label: "正定县", children: null },
          { value: "130125", label: "行唐县", children: null },
          { value: "130126", label: "灵寿县", children: null },
          { value: "130127", label: "高邑县", children: null },
          { value: "130128", label: "深泽县", children: null },
          { value: "130129", label: "赞皇县", children: null },
          { value: "130130", label: "无极县", children: null },
          { value: "130131", label: "平山县", children: null },
          { value: "130132", label: "元氏县", children: null },
          { value: "130133", label: "赵县", children: null },
          { value: "130181", label: "辛集市", children: null },
          { value: "130183", label: "晋州市", children: null },
          { value: "130184", label: "新乐市", children: null }
        ]
      }
    ]
  }
];

// 获取省份列表
export const getProvinces = (): AddressOption[] => {
  return ADDRESS_DATA.map(province => ({
    value: province.value,
    label: province.label
  }));
};

// 根据省份获取城市列表
export const getCities = (provinceValue: string): AddressOption[] => {
  const province = ADDRESS_DATA.find(p => p.value === provinceValue);
  return province?.children?.map(city => ({
    value: city.value,
    label: city.label
  })) || [];
};

// 根据城市获取区县列表
export const getDistricts = (cityValue: string): AddressOption[] => {
  for (const province of ADDRESS_DATA) {
    const city = province.children?.find(c => c.value === cityValue);
    if (city?.children) {
      return city.children;
    }
  }
  return [];
}; 