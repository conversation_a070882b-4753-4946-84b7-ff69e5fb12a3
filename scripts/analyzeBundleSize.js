const fs = require('fs');
const path = require('path');

/**
 * 分析构建产物大小
 */
function analyzeBundleSize() {
  const buildDir = path.join(__dirname, '../build');
  const jsDir = path.join(buildDir, 'js');
  
  if (!fs.existsSync(jsDir)) {
    console.log('❌ build/js 目录不存在，请先运行构建命令');
    return;
  }

  const files = fs.readdirSync(jsDir);
  const jsFiles = files.filter(file => file.endsWith('.js'));
  
  console.log('📊 JS文件大小分析:');
  console.log('='.repeat(50));
  
  let totalSize = 0;
  const fileSizes = [];
  
  jsFiles.forEach(file => {
    const filePath = path.join(jsDir, file);
    const stats = fs.statSync(filePath);
    const sizeInKB = (stats.size / 1024).toFixed(2);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    fileSizes.push({
      name: file,
      size: stats.size,
      sizeKB: sizeInKB,
      sizeMB: sizeInMB
    });
    
    totalSize += stats.size;
  });
  
  // 按大小排序
  fileSizes.sort((a, b) => b.size - a.size);
  
  fileSizes.forEach(file => {
    console.log(`${file.name.padEnd(30)} ${file.sizeKB.padStart(8)} KB (${file.sizeMB} MB)`);
  });
  
  console.log('='.repeat(50));
  console.log(`总大小: ${(totalSize / 1024).toFixed(2)} KB (${(totalSize / (1024 * 1024)).toFixed(2)} MB)`);
  
  // 优化建议
  console.log('\n💡 优化建议:');
  if (totalSize > 1024 * 1024) { // 大于1MB
    console.log('⚠️  总包大小超过1MB，建议:');
    console.log('   - 检查是否有不必要的依赖');
    console.log('   - 使用动态导入减少初始包大小');
    console.log('   - 优化图片和字体资源');
  }
  
  const largeFiles = fileSizes.filter(file => file.size > 200 * 1024); // 大于200KB
  if (largeFiles.length > 0) {
    console.log('⚠️  发现大文件:');
    largeFiles.forEach(file => {
      console.log(`   - ${file.name}: ${file.sizeKB} KB`);
    });
    console.log('   建议:');
    console.log('   - 检查是否可以进一步分割代码');
    console.log('   - 考虑使用CDN加载大型库');
  }
}

analyzeBundleSize(); 