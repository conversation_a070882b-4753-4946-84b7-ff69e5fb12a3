{"generatorType": "@nine/nine-generator-wm-web", "nineID": "5212", "builderType": "@nine/nine-preset-mt-wmb", "publishType": "@nine/nine-preset-mt-wmb", "preset": "@nine/nine-preset-mt-wmb", "localBuilderVersion": true, "appConfig": {"team": "other", "businessGroup": "store-business", "multipleCDN": false, "cdnConfig": {}, "iGateHostDir": "igate", "bucketName": "cagent-fe-h5-blindbox-d9d22fa0", "entry": [{"title": "奶茶盲盒", "name": "cagent", "src": "./src/projects/Main/index.tsx", "basename": "/cagent/blindbox", "terminal": "h5"}], "f1ApiConfig": [{"projectId": "900"}], "lxConfig": {"category": "waimai", "appnm": "waimai"}, "devServer": {"port": 10000, "proxy": {"/gw": {"target": "https://e.waimai.test.sankuai.com", "changeOrigin": true, "secure": false}, "/api": {"target": "http://ai.waimai.test.sankuai.com", "changeOrigin": true, "secure": false}, "/wmcagent": {"target": "https://s3plus-bj02.sankuai.com", "changeOrigin": true, "secure": false}}}, "buildType": "webpack", "wmbConfig": {"appKey": "com.sankuai.wmcagent.blindbox.h5", "harmonyUrlSetId": []}}}