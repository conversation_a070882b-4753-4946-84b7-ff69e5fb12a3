{"extends": "./src/.common/config/tsconfig.json", "compilerOptions": {"rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@common": ["src/.common"], "@common/*": ["src/.common/*"], "@baseUtils/*": ["src/.common/baseUtils/*"], "@assets/*": ["src/assets/*"], "@components/*": ["src/components/*"], "@projects/*": ["src/projects/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@constants/*": ["src/constants/*"], "@hooks/*": ["src/hooks/*"]}}, "include": ["src/.common", "src"]}