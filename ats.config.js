/**
 * 服务端接口TS化工具：https://km.sankuai.com/collabpage/2701152788
 * F1Api示例工程：https://f1-better.sankuai.com/#/home?projectId=900&api_project_id=53975&api_id=47465
 */
module.exports = {
  projects: [
    {
      projectId: '900',
    },
  ],
  requestCommonParamsList: ['acctId', 'wmPoiId', 'token', 'appType', 'deviceUUID', 'wm_appversion'],
  customizeRequestFilePath: '@common/wrapRequest',
  openApiRequestMis: 'duweijing',
  // 可以根据自己接口需要导出的目录进行修改
  // 标准化的MPA工程，可以放在./src/projects/${projectName}/services/下，其中projectName可随需求动态调整
  outputDir: './src/projects/Main/services',

  getRequestFunctionName: (interfaceInfo, changeCase) =>
    changeCase.camelCase(
      interfaceInfo.content.requestMethods.concat(interfaceInfo.path.split('/').filter(Boolean).slice(-3)).join(' '),
    ),
  outputFileName: (interfaceInfo, changeCase) =>
    `${changeCase.camelCase(
      interfaceInfo.content.requestMethods.concat(interfaceInfo.path.split('/').filter(Boolean).slice(-3)).join(' '),
    )}.ts`,
};
