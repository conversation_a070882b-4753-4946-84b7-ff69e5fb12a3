## cagent_fe_h5_blindbox

### 环境要求
* node：v18.20.4+
* pnpm：9.12.1

```sh
// 如pnpm未安装
mnpm install -g pnpm@9
```

### 启动和构建
```sh
// 下载依赖
pnpm install

// 启动
pnpm run start

// 双端复用-PC启动
pnpm run startMultiPC

// 双端复用-H5启动
pnpm run startMultiH5

// 构建
pnpm run build

// 规范检查
pnpm run check

// eslint批量格式化
pnpm run fix

// 执行单测
pnpm run test

// 新增接口，支持手动创建和自动创建
// 自动化创建TS接口，详细使用文档参考：https://km.sankuai.com/collabpage/2701152788
// 步骤1: 在F1Api平台新建工程和接口，示例接口 https://f1-better.sankuai.com/#/home?projectId=900&api_project_id=53975&api_id=47465
// 步骤2: 在nine.json中f1ApiConfig下的projectId，与F1Api平台新建的工程projectId一致
// 步骤3: 查找新增接口的api_project_id，执行下列脚本
pnpm run ats 53975
```

### 调试
```sh
# 启动时增加--bundleAnalyze参数，可查看打包产物分析数据
# 注意，暂不支持同时启动多端的产物分析，比如pnpm run startMulti --bundleAnalyze无效，请分别执行startMultiPC、startMultiH5的产物分析
pnpm run start --bundleAnalyze
pnpm run startMultiPC --bundleAnalyze
pnpm run startMultiH5 --bundleAnalyze
# 构建时调试打包产物，可使用下属指令
# 非多端构建
pnpm run build --bundleAnalyze
# 多端构建测试可参考 buildAnalyze 指令调试
pnpm run buildAnalyze
```


### 知识库使用
请确保您的preset套件版本在v1.0.2版本及以上
```sh
# 安装依赖
pnpm add @md/roo-b-mobile @md/roo-multiplex @md/roo-pc @md/standard-framework

# 创建/更新标准化知识库
npx knowledge gen

# 提交时过滤知识库。如您不希望标准化知识库提交到远程，可执行下列命令，后续会通过husky勾子在提交时过滤掉知识库相关的文件
echo "\nnode src/.common/scripts/ignoreKnowledge.js" >> .husky/pre-commit

# 删除标准化知识库
npx knowledge clean
```