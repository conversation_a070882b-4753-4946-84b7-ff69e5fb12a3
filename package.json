{"name": "cagent_fe_h5_blindbox", "version": "0.0.1", "description": "商家端React工程模板", "private": true, "author": "", "main": "index.js", "scripts": {"start": "nine dev", "start:mock": "nine dev --mock", "startMulti": "npm-run-all --parallel startPC startH5", "startPC": "MULTI_ENV=pc nine dev --hot-entry --mock", "startH5": "MULTI_ENV=h5 nine dev --hot-entry --mock", "build": "multiBuild", "check": "sh ./src/.common/scripts/check.sh", "fix": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "test": "jest --coverage", "ats": "ats", "initNine": "sh ./src/.common/scripts/initNine.sh", "postinstall": "node ./scripts/postinstall.js", "prepare": "husky", "buildTest": "export ROUTER_BASE=/ && export PUBLIC_PATH=www.meituan.com && export MULTI_ENV_BUILD=h5,pc && multiBuild", "buildAnalyze": "export ROUTER_BASE=/ && export PUBLIC_PATH=www.meituan.com && export MULTI_ENV=h5 && nine build --bundleAnalyze", "buildAnalyzePC": "export ROUTER_BASE=/ && export PUBLIC_PATH=www.meituan.com && export MULTI_ENV=pc && nine build --bundleAnalyze", "buildSize": "export ROUTER_BASE=/ && export PUBLIC_PATH=www.meituan.com && export MULTI_ENV=h5 && nine build && node scripts/analyzeBundleSize.js", "knowledgeGen": "knowledge gen && echo '\nnode src/.common/scripts/ignoreKnowledge.js' >> .husky/pre-commit", "knowledgeClean": "knowledge clean"}, "license": "ISC", "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@kl/eslint-plugin-kumi": "~2.1.1", "@kl/klint": "~2.1.0", "@md/roo-b-mobile": "^0.1.35", "@md/roo-multiplex": "^1.6.0", "@md/roo-pc": "^1.17.19", "@md/standard-framework": "0.0.7", "@mfe/eslint-config-unify": "~0.0.13", "@mfe/klint-plugin-common": "~2.1.3", "@mfe/standard-web-pro-report": "0.1.6", "@mfe/typescript-config-unify": "~1.0.5", "@nine/nine-plugin-multiplexing": "1.2.1", "@nine/nine-preset-mt-wmb": "1.1.0", "@testing-library/jest-dom": "6.6.2", "@testing-library/react": "16.0.1", "@testing-library/user-event": "14.5.2", "@types/big.js": "6.2.2", "@types/jest": "29.5.14", "@types/js-cookie": "3.0.6", "@types/lodash": "4.17.13", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-redux": "7.1.34", "eslint": "7.32.0", "husky": "9.1.6", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-transform-css": "4.0.1", "jest-transform-stub": "2.0.0", "lint-staged": "15.2.10", "npm-run-all": "^4.1.5", "stylelint": "^16.20.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "ts-jest": "29.2.5", "typescript": "5.1.6"}, "dependencies": {"@mfe/program-tool": "~1.1.1", "@reduxjs/toolkit": "2.2.6", "@roo/roo-b-mobile": "~0.1.50", "@roo/roo-multiplex": "~1.8.0", "@roo/roo-multiplex-h5": "~1.8.0", "@roo/roo-multiplex-pc": "~1.8.0", "@utiljs/basic": "^1.0.0", "@utiljs/business": "^1.0.2", "@utiljs/device": "^1.0.2", "@utiljs/env": "^1.0.2", "@utiljs/guid": "^1.0.5", "@utiljs/is": "^1.0.4", "@utiljs/json": "^0.2.7", "@utiljs/lx-report": "^1.0.2", "@utiljs/param": "^1.0.6", "@utiljs/raptor-report": "^1.0.1", "@utiljs/request-web": "^1.0.3", "@utiljs/route-tool": "^1.0.3", "@utiljs/storage": "^1.0.1", "@utiljs/string": "^1.0.3", "@waimai/slp-ats": "^1.0.11", "@wmfe/intelligent_common": "1.4.11", "ahooks": "3.8.1", "big.js": "6.2.2", "classnames": "2.5.1", "core-js": "3.22.3", "dayjs": "1.11.13", "framer-motion": "^12.18.1", "js-cookie": "3.0.5", "lodash": "4.17.21", "lottie-web": "^5.13.0", "postcss": "8.4.13", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.4.14"}, "repository": {}}